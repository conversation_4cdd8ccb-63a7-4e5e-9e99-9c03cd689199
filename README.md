# 📝 备忘录小工具

一个简洁实用的Windows备忘录应用，支持定时提醒、多任务管理和开机自启动。

## ✨ 功能特点

- 🎯 **多任务管理**: 支持添加、删除、查看多个备忘任务
- ⏰ **定时提醒**: 可设置具体的日期和时间进行提醒
- 🔊 **声音提醒**: 到达设定时间后播放系统提示音
- 🚀 **开机自启**: 支持Windows 11开机自动运行
- 🎨 **极简设计**: 清爽简洁的用户界面
- 💾 **数据持久化**: 自动保存备忘录数据

## 🛠️ 系统要求

- Windows 11 (或Windows 10)
- Python 3.7+
- 所有依赖均为Python标准库，无需额外安装

## 📦 安装使用

### 1. 下载代码
```bash
git clone <repository-url>
cd beiwanglu
```

### 2. 运行程序
```bash
python bwl.py
```

### 3. 设置开机自启动
```bash
python setup_autostart.py
```

## 📖 使用说明

### 添加备忘录
1. 在"备忘内容"文本框中输入要提醒的事项
2. 设置提醒的日期和时间
3. 点击"添加备忘"按钮

### 管理备忘录
- **查看详情**: 双击列表中的备忘录项目
- **删除备忘**: 选中项目后点击"删除选中"按钮
- **刷新列表**: 点击"刷新列表"按钮更新状态

### 提醒功能
- 程序会每30秒检查一次是否有到期的备忘录
- 到期时会播放系统提示音并弹出提醒窗口
- 提醒后状态会自动更新为"已提醒"

### 开机自启动
运行 `setup_autostart.py` 脚本：
- 选择"1"设置开机自启动
- 选择"2"移除开机自启动
- 选择"3"检查当前状态

## 📁 文件说明

- `bwl.py` - 主程序文件
- `setup_autostart.py` - 开机自启动设置工具
- `memo_data.json` - 备忘录数据存储文件（自动生成）
- `requirements.txt` - 依赖说明文件
- `README.md` - 使用说明文档

## 🎨 界面预览

程序采用极简设计风格：
- 清爽的浅色主题
- 简洁的布局设计
- 直观的操作界面
- 友好的提醒窗口

## ⚠️ 注意事项

1. **时间设置**: 提醒时间不能早于当前时间
2. **数据备份**: 重要备忘录建议定期备份 `memo_data.json` 文件
3. **权限要求**: 设置开机自启动需要修改注册表权限
4. **程序关闭**: 关闭程序前会询问确认，避免误操作

## 🔧 故障排除

### 程序无法启动
- 检查Python版本是否为3.7+
- 确认所有文件完整

### 声音提醒无效
- 检查系统音量设置
- 确认Windows声音方案正常

### 开机自启动失败
- 以管理员权限运行设置脚本
- 检查Windows用户账户控制设置

## 📝 更新日志

### v1.0.0
- 基础备忘录功能
- 定时提醒功能
- 开机自启动支持
- 极简UI设计

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

MIT License
