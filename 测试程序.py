#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
备忘录小工具 - 功能测试脚本
"""

import os
import sys
import json
from datetime import datetime, timedelta

def test_basic_functionality():
    """测试基本功能"""
    print("🧪 开始测试备忘录基本功能...")
    
    # 测试数据文件创建
    test_data = [
        {
            "id": 1,
            "content": "测试备忘录 - 这是一个测试用的备忘录内容",
            "remind_time": (datetime.now() + timedelta(minutes=1)).strftime("%Y-%m-%d %H:%M"),
            "status": "待提醒",
            "created_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        },
        {
            "id": 2,
            "content": "明天的会议",
            "remind_time": (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d %H:%M"),
            "status": "待提醒",
            "created_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
    ]
    
    # 创建测试数据文件
    try:
        with open("memo_data.json", "w", encoding="utf-8") as f:
            json.dump(test_data, f, ensure_ascii=False, indent=2)
        print("✅ 测试数据文件创建成功")
    except Exception as e:
        print(f"❌ 测试数据文件创建失败: {e}")
        return False
    
    # 验证数据文件读取
    try:
        with open("memo_data.json", "r", encoding="utf-8") as f:
            loaded_data = json.load(f)
        print(f"✅ 数据文件读取成功，包含 {len(loaded_data)} 条记录")
    except Exception as e:
        print(f"❌ 数据文件读取失败: {e}")
        return False
    
    return True

def test_file_structure():
    """测试文件结构"""
    print("\n📁 检查文件结构...")
    
    required_files = [
        "bwl.py",
        "setup_autostart.py", 
        "requirements.txt",
        "README.md",
        "启动备忘录.bat"
    ]
    
    optional_files = [
        "bwl_enhanced.py",
        "安装指南.md"
    ]
    
    all_good = True
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file} - 存在")
        else:
            print(f"❌ {file} - 缺失")
            all_good = False
    
    for file in optional_files:
        if os.path.exists(file):
            print(f"✅ {file} - 存在（可选）")
        else:
            print(f"⚠️ {file} - 缺失（可选）")
    
    return all_good

def test_python_imports():
    """测试Python模块导入"""
    print("\n🐍 测试Python模块导入...")
    
    required_modules = [
        "tkinter",
        "datetime", 
        "json",
        "threading",
        "time",
        "os",
        "sys",
        "winsound"
    ]
    
    all_good = True
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module} - 导入成功")
        except ImportError as e:
            print(f"❌ {module} - 导入失败: {e}")
            all_good = False
    
    # 测试可选模块
    optional_modules = ["pystray", "PIL"]
    for module in optional_modules:
        try:
            __import__(module)
            print(f"✅ {module} - 导入成功（增强功能可用）")
        except ImportError:
            print(f"⚠️ {module} - 导入失败（增强功能不可用）")
    
    return all_good

def test_program_syntax():
    """测试程序语法"""
    print("\n🔍 检查程序语法...")
    
    programs = ["bwl.py", "setup_autostart.py"]
    if os.path.exists("bwl_enhanced.py"):
        programs.append("bwl_enhanced.py")
    
    all_good = True
    
    for program in programs:
        try:
            with open(program, 'r', encoding='utf-8') as f:
                code = f.read()
            compile(code, program, 'exec')
            print(f"✅ {program} - 语法检查通过")
        except SyntaxError as e:
            print(f"❌ {program} - 语法错误: {e}")
            all_good = False
        except Exception as e:
            print(f"⚠️ {program} - 检查异常: {e}")
    
    return all_good

def generate_test_report():
    """生成测试报告"""
    print("\n" + "="*50)
    print("📊 备忘录小工具 - 测试报告")
    print("="*50)
    
    tests = [
        ("文件结构检查", test_file_structure),
        ("Python模块导入", test_python_imports), 
        ("程序语法检查", test_program_syntax),
        ("基本功能测试", test_basic_functionality)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔄 执行: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "="*50)
    print("📋 测试结果汇总")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！程序可以正常使用。")
        print("\n🚀 快速开始:")
        print("1. 运行: python bwl.py")
        print("2. 或双击: 启动备忘录.bat")
        print("3. 设置开机自启: python setup_autostart.py")
    else:
        print("⚠️ 部分测试失败，请检查相关问题。")
    
    return passed == total

def main():
    """主函数"""
    print("🔧 备忘录小工具 - 自动测试程序")
    print("=" * 50)
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"🐍 Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 7):
        print("❌ Python版本过低，需要3.7或更高版本")
        return False
    else:
        print("✅ Python版本符合要求")
    
    # 运行测试
    return generate_test_report()

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
