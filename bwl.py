#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
备忘录小工具 - 带日期提醒功能
支持多任务管理、开机自启、极简UI设计
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
import json
import threading
import time
import os
import sys
import winsound
from pathlib import Path

class MemoApp:
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.data_file = "memo_data.json"
        self.memos = self.load_memos()
        self.setup_ui()
        self.start_reminder_thread()

    def setup_window(self):
        """设置主窗口"""
        self.root.title("备忘录")
        self.root.geometry("400x500")
        self.root.resizable(False, False)

        # 极简风格配色
        self.bg_color = "#f8f9fa"
        self.primary_color = "#007bff"
        self.text_color = "#212529"
        self.border_color = "#dee2e6"

        self.root.configure(bg=self.bg_color)

        # 设置样式
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('Custom.TFrame', background=self.bg_color)
        style.configure('Custom.TLabel', background=self.bg_color, foreground=self.text_color)
        style.configure('Custom.TButton', padding=6)

    def setup_ui(self):
        """设置用户界面"""
        # 主容器
        main_frame = ttk.Frame(self.root, style='Custom.TFrame', padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = ttk.Label(main_frame, text="📝 备忘录",
                               font=("Microsoft YaHei", 16, "bold"),
                               style='Custom.TLabel')
        title_label.pack(pady=(0, 20))

        # 添加备忘录区域
        self.setup_add_memo_section(main_frame)

        # 备忘录列表区域
        self.setup_memo_list_section(main_frame)

        # 底部按钮
        self.setup_bottom_buttons(main_frame)

    def setup_add_memo_section(self, parent):
        """设置添加备忘录区域"""
        add_frame = ttk.LabelFrame(parent, text="添加新备忘", padding="10")
        add_frame.pack(fill=tk.X, pady=(0, 15))

        # 备忘内容输入
        ttk.Label(add_frame, text="备忘内容:").pack(anchor=tk.W)
        self.memo_text = tk.Text(add_frame, height=3, width=40,
                                font=("Microsoft YaHei", 10),
                                relief=tk.FLAT, bd=1)
        self.memo_text.pack(fill=tk.X, pady=(5, 10))

        # 日期时间设置
        datetime_frame = ttk.Frame(add_frame)
        datetime_frame.pack(fill=tk.X, pady=(0, 10))

        # 日期选择
        date_frame = ttk.Frame(datetime_frame)
        date_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

        ttk.Label(date_frame, text="提醒日期:").pack(anchor=tk.W)

        date_input_frame = ttk.Frame(date_frame)
        date_input_frame.pack(fill=tk.X, pady=(2, 0))

        # 年月日输入
        now = datetime.now()
        self.year_var = tk.StringVar(value=str(now.year))
        self.month_var = tk.StringVar(value=str(now.month))
        self.day_var = tk.StringVar(value=str(now.day))

        year_spin = ttk.Spinbox(date_input_frame, from_=2024, to=2030,
                               width=6, textvariable=self.year_var)
        year_spin.pack(side=tk.LEFT, padx=(0, 2))

        ttk.Label(date_input_frame, text="年").pack(side=tk.LEFT, padx=(0, 5))

        month_spin = ttk.Spinbox(date_input_frame, from_=1, to=12,
                                width=4, textvariable=self.month_var)
        month_spin.pack(side=tk.LEFT, padx=(0, 2))

        ttk.Label(date_input_frame, text="月").pack(side=tk.LEFT, padx=(0, 5))

        day_spin = ttk.Spinbox(date_input_frame, from_=1, to=31,
                              width=4, textvariable=self.day_var)
        day_spin.pack(side=tk.LEFT, padx=(0, 2))

        ttk.Label(date_input_frame, text="日").pack(side=tk.LEFT)

        # 时间选择
        time_frame = ttk.Frame(datetime_frame)
        time_frame.pack(side=tk.RIGHT, padx=(20, 0))

        ttk.Label(time_frame, text="提醒时间:").pack(anchor=tk.W)

        time_input_frame = ttk.Frame(time_frame)
        time_input_frame.pack(fill=tk.X, pady=(2, 0))

        self.hour_var = tk.StringVar(value=str(now.hour))
        self.minute_var = tk.StringVar(value=str(now.minute))

        hour_spin = ttk.Spinbox(time_input_frame, from_=0, to=23,
                               width=4, textvariable=self.hour_var)
        hour_spin.pack(side=tk.LEFT, padx=(0, 2))

        ttk.Label(time_input_frame, text=":").pack(side=tk.LEFT, padx=(0, 2))

        minute_spin = ttk.Spinbox(time_input_frame, from_=0, to=59,
                                 width=4, textvariable=self.minute_var)
        minute_spin.pack(side=tk.LEFT)

        # 添加按钮
        add_btn = ttk.Button(add_frame, text="添加备忘",
                            command=self.add_memo)
        add_btn.pack(pady=(10, 0))

    def setup_memo_list_section(self, parent):
        """设置备忘录列表区域"""
        list_frame = ttk.LabelFrame(parent, text="备忘录列表", padding="10")
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        # 创建Treeview
        columns = ("content", "datetime", "status")
        self.memo_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=10)

        # 设置列标题和宽度
        self.memo_tree.heading("content", text="备忘内容")
        self.memo_tree.heading("datetime", text="提醒时间")
        self.memo_tree.heading("status", text="状态")

        self.memo_tree.column("content", width=200)
        self.memo_tree.column("datetime", width=120)
        self.memo_tree.column("status", width=60)

        # 滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.memo_tree.yview)
        self.memo_tree.configure(yscrollcommand=scrollbar.set)

        self.memo_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 绑定双击事件
        self.memo_tree.bind("<Double-1>", self.on_memo_double_click)

        self.refresh_memo_list()

    def setup_bottom_buttons(self, parent):
        """设置底部按钮"""
        button_frame = ttk.Frame(parent, style='Custom.TFrame')
        button_frame.pack(fill=tk.X)

        delete_btn = ttk.Button(button_frame, text="删除选中",
                               command=self.delete_selected_memo)
        delete_btn.pack(side=tk.LEFT, padx=(0, 10))

        refresh_btn = ttk.Button(button_frame, text="刷新列表",
                                command=self.refresh_memo_list)
        refresh_btn.pack(side=tk.LEFT, padx=(0, 10))

        minimize_btn = ttk.Button(button_frame, text="最小化到托盘",
                                 command=self.minimize_to_tray)
        minimize_btn.pack(side=tk.RIGHT)

    def add_memo(self):
        """添加新备忘录"""
        content = self.memo_text.get("1.0", tk.END).strip()
        if not content:
            messagebox.showwarning("警告", "请输入备忘内容！")
            return

        try:
            year = int(self.year_var.get())
            month = int(self.month_var.get())
            day = int(self.day_var.get())
            hour = int(self.hour_var.get())
            minute = int(self.minute_var.get())

            remind_time = datetime(year, month, day, hour, minute)

            if remind_time <= datetime.now():
                messagebox.showwarning("警告", "提醒时间不能早于当前时间！")
                return

        except ValueError:
            messagebox.showerror("错误", "请输入正确的日期时间！")
            return

        # 创建新备忘录
        memo = {
            "id": len(self.memos) + 1,
            "content": content,
            "remind_time": remind_time.strftime("%Y-%m-%d %H:%M"),
            "status": "待提醒",
            "created_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

        self.memos.append(memo)
        self.save_memos()
        self.refresh_memo_list()

        # 清空输入框
        self.memo_text.delete("1.0", tk.END)

        messagebox.showinfo("成功", "备忘录添加成功！")

    def delete_selected_memo(self):
        """删除选中的备忘录"""
        selected = self.memo_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请选择要删除的备忘录！")
            return

        if messagebox.askyesno("确认", "确定要删除选中的备忘录吗？"):
            for item in selected:
                values = self.memo_tree.item(item)["values"]
                content = values[0]
                remind_time = values[1]
                # 根据内容和时间找到对应的备忘录
                for i, memo in enumerate(self.memos):
                    if (memo["content"][:30] + "..." if len(memo["content"]) > 30 else memo["content"]) == content and memo["remind_time"] == remind_time:
                        self.memos.pop(i)
                        break

            self.save_memos()
            self.refresh_memo_list()
            messagebox.showinfo("成功", "备忘录删除成功！")

    def on_memo_double_click(self, event):
        """双击备忘录事件"""
        selected = self.memo_tree.selection()
        if selected:
            item = selected[0]
            values = self.memo_tree.item(item)["values"]
            content = values[0]
            remind_time = values[1]
            status = values[2]

            # 显示详细信息
            detail_window = tk.Toplevel(self.root)
            detail_window.title("备忘录详情")
            detail_window.geometry("300x200")
            detail_window.resizable(False, False)

            ttk.Label(detail_window, text="备忘内容:", font=("Microsoft YaHei", 10, "bold")).pack(anchor=tk.W, padx=10, pady=(10, 5))
            content_text = tk.Text(detail_window, height=4, width=35, font=("Microsoft YaHei", 9))
            content_text.pack(padx=10, pady=(0, 10))
            content_text.insert("1.0", content)
            content_text.config(state=tk.DISABLED)

            ttk.Label(detail_window, text=f"提醒时间: {remind_time}", font=("Microsoft YaHei", 9)).pack(anchor=tk.W, padx=10)
            ttk.Label(detail_window, text=f"状态: {status}", font=("Microsoft YaHei", 9)).pack(anchor=tk.W, padx=10, pady=(5, 10))

    def refresh_memo_list(self):
        """刷新备忘录列表"""
        # 清空现有项目
        for item in self.memo_tree.get_children():
            self.memo_tree.delete(item)

        # 添加备忘录到列表
        for memo in self.memos:
            # 检查是否过期
            remind_time = datetime.strptime(memo["remind_time"], "%Y-%m-%d %H:%M")
            if remind_time <= datetime.now() and memo["status"] == "待提醒":
                memo["status"] = "已过期"

            self.memo_tree.insert("", tk.END, values=(
                memo["content"][:30] + "..." if len(memo["content"]) > 30 else memo["content"],
                memo["remind_time"],
                memo["status"]
            ))

    def load_memos(self):
        """加载备忘录数据"""
        try:
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"加载数据失败: {e}")
        return []

    def save_memos(self):
        """保存备忘录数据"""
        try:
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(self.memos, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存数据失败: {e}")

    def start_reminder_thread(self):
        """启动提醒线程"""
        self.reminder_thread = threading.Thread(target=self.reminder_loop, daemon=True)
        self.reminder_thread.start()

    def reminder_loop(self):
        """提醒循环"""
        while True:
            try:
                current_time = datetime.now()
                for memo in self.memos:
                    if memo["status"] == "待提醒":
                        remind_time = datetime.strptime(memo["remind_time"], "%Y-%m-%d %H:%M")
                        if current_time >= remind_time:
                            self.show_reminder(memo)
                            memo["status"] = "已提醒"
                            self.save_memos()

                time.sleep(30)  # 每30秒检查一次
            except Exception as e:
                print(f"提醒线程错误: {e}")
                time.sleep(60)

    def show_reminder(self, memo):
        """显示提醒"""
        # 播放提醒声音
        try:
            winsound.MessageBeep(winsound.MB_ICONEXCLAMATION)
        except:
            pass

        # 显示提醒窗口
        self.root.after(0, lambda: self.show_reminder_window(memo))

    def show_reminder_window(self, memo):
        """显示提醒窗口"""
        reminder_window = tk.Toplevel(self.root)
        reminder_window.title("⏰ 备忘提醒")
        reminder_window.geometry("350x200")
        reminder_window.resizable(False, False)
        reminder_window.attributes("-topmost", True)

        # 居中显示
        reminder_window.transient(self.root)
        reminder_window.grab_set()

        main_frame = ttk.Frame(reminder_window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 提醒图标和标题
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(title_frame, text="⏰", font=("Microsoft YaHei", 20)).pack(side=tk.LEFT)
        ttk.Label(title_frame, text="备忘提醒", font=("Microsoft YaHei", 14, "bold")).pack(side=tk.LEFT, padx=(10, 0))

        # 备忘内容
        content_frame = ttk.LabelFrame(main_frame, text="备忘内容", padding="10")
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        content_text = tk.Text(content_frame, height=4, width=35, font=("Microsoft YaHei", 10))
        content_text.pack(fill=tk.BOTH, expand=True)
        content_text.insert("1.0", memo["content"])
        content_text.config(state=tk.DISABLED)

        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        ttk.Button(button_frame, text="知道了", command=reminder_window.destroy).pack(side=tk.RIGHT)

    def minimize_to_tray(self):
        """最小化到系统托盘"""
        self.root.withdraw()
        messagebox.showinfo("提示", "程序已最小化到系统托盘，双击托盘图标可恢复窗口")

    def run(self):
        """运行应用程序"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

    def on_closing(self):
        """关闭程序时的处理"""
        if messagebox.askokcancel("退出", "确定要退出备忘录程序吗？"):
            self.root.destroy()


def main():
    """主函数"""
    app = MemoApp()
    app.run()


if __name__ == "__main__":
    main()