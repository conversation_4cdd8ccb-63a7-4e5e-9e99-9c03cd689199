#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建备忘录程序图标
"""

try:
    from PIL import Image, ImageDraw, ImageFont
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

def create_memo_icon():
    """创建备忘录图标"""
    if not PIL_AVAILABLE:
        print("⚠️ PIL/Pillow未安装，无法创建图标")
        print("可以运行: pip install Pillow")
        return False
    
    try:
        # 创建256x256的图标
        size = 256
        image = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(image)
        
        # 绘制背景圆形
        margin = 20
        circle_bbox = [margin, margin, size-margin, size-margin]
        draw.ellipse(circle_bbox, fill=(0, 123, 255, 255), outline=(0, 100, 200, 255), width=4)
        
        # 绘制备忘录图标
        # 绘制纸张背景
        paper_margin = 60
        paper_bbox = [paper_margin, paper_margin, size-paper_margin, size-paper_margin-20]
        draw.rectangle(paper_bbox, fill=(255, 255, 255, 255), outline=(200, 200, 200, 255), width=2)
        
        # 绘制文本行
        line_color = (100, 100, 100, 255)
        line_width = 2
        for i in range(4):
            y = paper_margin + 30 + i * 25
            draw.rectangle([paper_margin + 20, y, size - paper_margin - 20, y + line_width], 
                         fill=line_color)
        
        # 绘制钟表图标（表示提醒功能）
        clock_center_x = size - 50
        clock_center_y = 50
        clock_radius = 20
        
        # 钟表外圈
        clock_bbox = [clock_center_x - clock_radius, clock_center_y - clock_radius,
                     clock_center_x + clock_radius, clock_center_y + clock_radius]
        draw.ellipse(clock_bbox, fill=(255, 255, 255, 255), outline=(255, 0, 0, 255), width=3)
        
        # 钟表指针
        draw.line([clock_center_x, clock_center_y, clock_center_x, clock_center_y - 12], 
                 fill=(255, 0, 0, 255), width=2)  # 时针
        draw.line([clock_center_x, clock_center_y, clock_center_x + 8, clock_center_y], 
                 fill=(255, 0, 0, 255), width=2)  # 分针
        
        # 保存为ICO格式
        # 创建多个尺寸的图标
        sizes = [256, 128, 64, 48, 32, 16]
        images = []
        
        for icon_size in sizes:
            resized = image.resize((icon_size, icon_size), Image.Resampling.LANCZOS)
            images.append(resized)
        
        # 保存ICO文件
        images[0].save('memo.ico', format='ICO', sizes=[(img.width, img.height) for img in images])
        
        print("✅ 图标文件 memo.ico 创建成功")
        return True
        
    except Exception as e:
        print(f"❌ 创建图标失败: {e}")
        return False

def main():
    """主函数"""
    print("🎨 创建备忘录程序图标...")
    success = create_memo_icon()
    
    if success:
        print("🎉 图标创建完成！")
    else:
        print("⚠️ 图标创建失败，将使用默认图标")

if __name__ == "__main__":
    main()
