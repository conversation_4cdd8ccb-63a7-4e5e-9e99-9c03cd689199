# -*- mode: python ; coding: utf-8 -*-
"""
智能备忘录高级版 PyInstaller 配置文件
包含自定义声音、科技感UI等新功能
"""

import sys
from pathlib import Path

# 获取当前目录
current_dir = Path.cwd()

# 分析高级版程序
a = Analysis(
    ['bwl_advanced.py'],
    pathex=[str(current_dir)],
    binaries=[],
    datas=[
        # 包含文档文件
        ('高级版功能说明.md', '.'),
        ('README.md', '.'),
        ('安装指南.md', '.'),
        # 包含自定义声音目录（如果存在）
        ('custom_sounds', 'custom_sounds') if Path('custom_sounds').exists() else ('custom_sounds', 'custom_sounds'),
    ],
    hiddenimports=[
        # 确保包含所有需要的模块
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.filedialog',
        'datetime',
        'json',
        'threading',
        'time',
        'os',
        'sys',
        'winsound',
        'pathlib',
        'winreg',
        'shutil',
        # pygame音频支持
        'pygame',
        'pygame.mixer',
        # 系统托盘功能（可选）
        'pystray',
        'PIL',
        'PIL.Image',
        'PIL.ImageDraw',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # 排除不需要的模块以减小体积
        'matplotlib',
        'numpy',
        'scipy',
        'pandas',
        'test',
        'unittest',
        'distutils',
        'setuptools',
        'email',
        'http',
        'urllib',
        'xml',
        'html',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# 处理PYZ
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# 创建EXE
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='智能备忘录高级版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,  # 使用UPX压缩（如果可用）
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='memo.ico' if Path('memo.ico').exists() else None,  # 如果有图标文件
    version_file='version_info_advanced.txt' if Path('version_info_advanced.txt').exists() else None,
)
