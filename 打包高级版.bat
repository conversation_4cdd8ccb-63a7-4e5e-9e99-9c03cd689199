@echo off
chcp 65001 >nul
title 智能备忘录高级版 - 打包工具

echo.
echo ========================================
echo     🔮 智能备忘录高级版 - 打包工具
echo ========================================
echo.

echo 🔍 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    pause
    exit /b 1
)

echo ✅ Python环境正常

echo.
echo 🔍 检查PyInstaller...
python -c "import PyInstaller; print('PyInstaller版本:', PyInstaller.__version__)" 2>nul
if errorlevel 1 (
    echo ❌ PyInstaller未安装
    echo 正在安装PyInstaller...
    pip install pyinstaller
    if errorlevel 1 (
        echo ❌ PyInstaller安装失败
        pause
        exit /b 1
    )
)

echo ✅ PyInstaller已准备就绪

echo.
echo 🔍 检查高级版依赖...
python -c "import pygame" 2>nul
if errorlevel 1 (
    echo ⚠️ pygame未安装，正在安装...
    pip install pygame
)

python -c "import pystray, PIL" 2>nul
if errorlevel 1 (
    echo ⚠️ 系统托盘依赖未安装，正在安装...
    pip install pystray pillow
)

echo ✅ 依赖检查完成

echo.
echo 🧹 清理旧文件...
if exist build rmdir /s /q build
if exist dist rmdir /s /q dist
if exist __pycache__ rmdir /s /q __pycache__

echo.
echo 🎨 创建图标文件...
python create_icon.py

echo.
echo 🔨 开始打包高级版...
echo 命令: pyinstaller --clean --noconfirm memo_advanced.spec
python -m PyInstaller --clean --noconfirm memo_advanced.spec

if errorlevel 1 (
    echo ❌ 高级版打包失败
    pause
    exit /b 1
) else (
    echo ✅ 高级版打包成功
)

echo.
echo 📦 创建高级版便携包...
if not exist 高级版便携包 mkdir 高级版便携包

if exist dist\智能备忘录高级版.exe (
    copy dist\智能备忘录高级版.exe 高级版便携包\
    echo   复制: 智能备忘录高级版.exe
)

if exist dist\开机自启动设置.exe (
    copy dist\开机自启动设置.exe 高级版便携包\
    echo   复制: 开机自启动设置.exe
)

if exist 高级版功能说明.md (
    copy 高级版功能说明.md 高级版便携包\
    echo   复制: 高级版功能说明.md
)

if exist README.md (
    copy README.md 高级版便携包\
    echo   复制: README.md
)

if exist 安装指南.md (
    copy 安装指南.md 高级版便携包\
    echo   复制: 安装指南.md
)

echo.
echo 📝 创建高级版使用说明...
(
echo # 🔮 智能备忘录高级版 - 便携版
echo.
echo ## 🚀 快速开始
echo.
echo 1. **运行程序**: 双击 `智能备忘录高级版.exe` 启动程序
echo 2. **开机自启**: 双击 `开机自启动设置.exe` 设置开机自启动
echo 3. **查看说明**: 阅读 `高级版功能说明.md` 了解新功能
echo.
echo ## ✨ 新功能特色
echo.
echo - 🔊 **自定义声音**: 上传个人音频文件作为提醒声音
echo - 🗑️ **自由删除**: 右键菜单和批量删除功能
echo - ⏰ **精确时间**: 时间设置精确到分钟级别
echo - 🎨 **科技感UI**: 深色主题和现代化界面设计
echo.
echo ## ⚠️ 注意事项
echo.
echo 1. **首次运行**: 可能被杀毒软件拦截，请添加信任
echo 2. **声音文件**: 支持WAV、MP3、OGG格式
echo 3. **数据保存**: 程序数据保存在同目录下
echo 4. **系统要求**: Windows 10/11，建议内存512MB以上
echo.
echo ## 📞 技术支持
echo.
echo 详细功能说明请查看 `高级版功能说明.md` 文件
echo.
echo ---
echo 🎉 感谢使用智能备忘录高级版！
) > 高级版便携包\使用说明.txt

echo   创建: 使用说明.txt

echo.
echo ========================================
echo 📊 打包完成总结
echo ========================================

echo.
echo 📁 生成的文件:
if exist dist (
    dir /b dist\*.exe
)

echo.
echo 📦 高级版便携包:
if exist 高级版便携包 (
    dir /b 高级版便携包\*.*
)

echo.
echo 🎉 高级版打包完成！
echo 📂 可执行文件位置: dist\
echo 📦 便携版位置: 高级版便携包\
echo.
echo ✨ 新功能特色:
echo   🔊 自定义声音上传
echo   🗑️ 任务自由删除
echo   ⏰ 时间精确到分钟
echo   🎨 科技感金属简约风格UI
echo.
echo ⚠️ 注意事项:
echo 1. 首次运行可能被杀毒软件拦截，请添加信任
echo 2. 程序数据保存在memo_data.json和app_config.json
echo 3. 自定义声音保存在custom_sounds目录
echo 4. 开机自启动需要管理员权限

pause
