#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
备忘录小工具 - 增强版
支持真正的系统托盘功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
import json
import threading
import time
import os
import sys
import winsound
from pathlib import Path

try:
    import pystray
    from PIL import Image, ImageDraw
    TRAY_AVAILABLE = True
except ImportError:
    TRAY_AVAILABLE = False

class MemoApp:
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.data_file = "memo_data.json"
        self.memos = self.load_memos()
        self.setup_ui()
        self.start_reminder_thread()
        self.tray_icon = None

    def setup_window(self):
        """设置主窗口"""
        self.root.title("📝 备忘录")
        self.root.geometry("450x550")
        self.root.resizable(False, False)

        # 极简风格配色
        self.bg_color = "#f8f9fa"
        self.primary_color = "#007bff"
        self.text_color = "#212529"
        self.border_color = "#dee2e6"

        self.root.configure(bg=self.bg_color)

        # 设置样式
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('Custom.TFrame', background=self.bg_color)
        style.configure('Custom.TLabel', background=self.bg_color, foreground=self.text_color)
        style.configure('Custom.TButton', padding=6)

        # 设置窗口图标（如果有的话）
        try:
            self.root.iconbitmap(default='memo.ico')
        except:
            pass

    def setup_ui(self):
        """设置用户界面"""
        # 主容器
        main_frame = ttk.Frame(self.root, style='Custom.TFrame', padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_frame = ttk.Frame(main_frame, style='Custom.TFrame')
        title_frame.pack(fill=tk.X, pady=(0, 20))

        title_label = ttk.Label(title_frame, text="📝 备忘录",
                               font=("Microsoft YaHei", 18, "bold"),
                               style='Custom.TLabel')
        title_label.pack(side=tk.LEFT)

        # 状态标签
        self.status_label = ttk.Label(title_frame, text="",
                                     font=("Microsoft YaHei", 9),
                                     style='Custom.TLabel')
        self.status_label.pack(side=tk.RIGHT)

        # 添加备忘录区域
        self.setup_add_memo_section(main_frame)

        # 备忘录列表区域
        self.setup_memo_list_section(main_frame)

        # 底部按钮
        self.setup_bottom_buttons(main_frame)

        # 更新状态
        self.update_status()

    def setup_add_memo_section(self, parent):
        """设置添加备忘录区域"""
        add_frame = ttk.LabelFrame(parent, text="✏️ 添加新备忘", padding="15")
        add_frame.pack(fill=tk.X, pady=(0, 15))

        # 备忘内容输入
        ttk.Label(add_frame, text="备忘内容:").pack(anchor=tk.W)
        self.memo_text = tk.Text(add_frame, height=3, width=45,
                                font=("Microsoft YaHei", 10),
                                relief=tk.SOLID, bd=1)
        self.memo_text.pack(fill=tk.X, pady=(5, 15))

        # 日期时间设置
        datetime_frame = ttk.Frame(add_frame)
        datetime_frame.pack(fill=tk.X, pady=(0, 15))

        # 日期选择
        date_frame = ttk.Frame(datetime_frame)
        date_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

        ttk.Label(date_frame, text="📅 提醒日期:").pack(anchor=tk.W)

        date_input_frame = ttk.Frame(date_frame)
        date_input_frame.pack(fill=tk.X, pady=(5, 0))

        # 年月日输入
        now = datetime.now()
        self.year_var = tk.StringVar(value=str(now.year))
        self.month_var = tk.StringVar(value=str(now.month))
        self.day_var = tk.StringVar(value=str(now.day))

        year_spin = ttk.Spinbox(date_input_frame, from_=2024, to=2030,
                               width=6, textvariable=self.year_var)
        year_spin.pack(side=tk.LEFT, padx=(0, 3))

        ttk.Label(date_input_frame, text="年").pack(side=tk.LEFT, padx=(0, 8))

        month_spin = ttk.Spinbox(date_input_frame, from_=1, to=12,
                                width=4, textvariable=self.month_var)
        month_spin.pack(side=tk.LEFT, padx=(0, 3))

        ttk.Label(date_input_frame, text="月").pack(side=tk.LEFT, padx=(0, 8))

        day_spin = ttk.Spinbox(date_input_frame, from_=1, to=31,
                              width=4, textvariable=self.day_var)
        day_spin.pack(side=tk.LEFT, padx=(0, 3))

        ttk.Label(date_input_frame, text="日").pack(side=tk.LEFT)

        # 时间选择
        time_frame = ttk.Frame(datetime_frame)
        time_frame.pack(side=tk.RIGHT, padx=(20, 0))

        ttk.Label(time_frame, text="🕐 提醒时间:").pack(anchor=tk.W)

        time_input_frame = ttk.Frame(time_frame)
        time_input_frame.pack(fill=tk.X, pady=(5, 0))

        self.hour_var = tk.StringVar(value=str(now.hour))
        self.minute_var = tk.StringVar(value=str(now.minute))

        hour_spin = ttk.Spinbox(time_input_frame, from_=0, to=23,
                               width=4, textvariable=self.hour_var)
        hour_spin.pack(side=tk.LEFT, padx=(0, 3))

        ttk.Label(time_input_frame, text=":").pack(side=tk.LEFT, padx=(0, 3))

        minute_spin = ttk.Spinbox(time_input_frame, from_=0, to=59,
                                 width=4, textvariable=self.minute_var)
        minute_spin.pack(side=tk.LEFT)

        # 快捷时间按钮
        quick_time_frame = ttk.Frame(add_frame)
        quick_time_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(quick_time_frame, text="⚡ 快捷设置:").pack(side=tk.LEFT)

        ttk.Button(quick_time_frame, text="1小时后",
                  command=lambda: self.set_quick_time(1)).pack(side=tk.LEFT, padx=(10, 5))
        ttk.Button(quick_time_frame, text="明天",
                  command=lambda: self.set_quick_time(24)).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(quick_time_frame, text="一周后",
                  command=lambda: self.set_quick_time(24*7)).pack(side=tk.LEFT)

        # 添加按钮
        add_btn = ttk.Button(add_frame, text="➕ 添加备忘",
                            command=self.add_memo)
        add_btn.pack(pady=(5, 0))

    def set_quick_time(self, hours):
        """设置快捷时间"""
        target_time = datetime.now() + timedelta(hours=hours)
        self.year_var.set(str(target_time.year))
        self.month_var.set(str(target_time.month))
        self.day_var.set(str(target_time.day))
        self.hour_var.set(str(target_time.hour))
        self.minute_var.set(str(target_time.minute))

    def setup_memo_list_section(self, parent):
        """设置备忘录列表区域"""
        list_frame = ttk.LabelFrame(parent, text="📋 备忘录列表", padding="15")
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        # 创建Treeview
        columns = ("content", "datetime", "status")
        self.memo_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=12)

        # 设置列标题和宽度
        self.memo_tree.heading("content", text="备忘内容")
        self.memo_tree.heading("datetime", text="提醒时间")
        self.memo_tree.heading("status", text="状态")

        self.memo_tree.column("content", width=220)
        self.memo_tree.column("datetime", width=130)
        self.memo_tree.column("status", width=70)

        # 滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.memo_tree.yview)
        self.memo_tree.configure(yscrollcommand=scrollbar.set)

        self.memo_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 绑定双击事件
        self.memo_tree.bind("<Double-1>", self.on_memo_double_click)

        self.refresh_memo_list()

    def setup_bottom_buttons(self, parent):
        """设置底部按钮"""
        button_frame = ttk.Frame(parent, style='Custom.TFrame')
        button_frame.pack(fill=tk.X)

        delete_btn = ttk.Button(button_frame, text="🗑️ 删除选中",
                               command=self.delete_selected_memo)
        delete_btn.pack(side=tk.LEFT, padx=(0, 10))

        refresh_btn = ttk.Button(button_frame, text="🔄 刷新列表",
                                command=self.refresh_memo_list)
        refresh_btn.pack(side=tk.LEFT, padx=(0, 10))

        if TRAY_AVAILABLE:
            minimize_btn = ttk.Button(button_frame, text="📌 最小化到托盘",
                                     command=self.minimize_to_tray)
            minimize_btn.pack(side=tk.RIGHT)
        else:
            hide_btn = ttk.Button(button_frame, text="🔽 隐藏窗口",
                                 command=self.hide_window)
            hide_btn.pack(side=tk.RIGHT)

    def update_status(self):
        """更新状态信息"""
        total = len(self.memos)
        pending = len([m for m in self.memos if m["status"] == "待提醒"])
        self.status_label.config(text=f"总计: {total} | 待提醒: {pending}")

        # 定期更新
        self.root.after(60000, self.update_status)  # 每分钟更新一次

    def add_memo(self):
        """添加新备忘录"""
        content = self.memo_text.get("1.0", tk.END).strip()
        if not content:
            messagebox.showwarning("警告", "请输入备忘内容！")
            return

        try:
            year = int(self.year_var.get())
            month = int(self.month_var.get())
            day = int(self.day_var.get())
            hour = int(self.hour_var.get())
            minute = int(self.minute_var.get())

            remind_time = datetime(year, month, day, hour, minute)

            if remind_time <= datetime.now():
                messagebox.showwarning("警告", "提醒时间不能早于当前时间！")
                return

        except ValueError:
            messagebox.showerror("错误", "请输入正确的日期时间！")
            return

        # 创建新备忘录
        memo = {
            "id": int(time.time() * 1000),  # 使用时间戳作为唯一ID
            "content": content,
            "remind_time": remind_time.strftime("%Y-%m-%d %H:%M"),
            "status": "待提醒",
            "created_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

        self.memos.append(memo)
        self.save_memos()
        self.refresh_memo_list()
        self.update_status()

        # 清空输入框
        self.memo_text.delete("1.0", tk.END)

        messagebox.showinfo("成功", "备忘录添加成功！")

    def delete_selected_memo(self):
        """删除选中的备忘录"""
        selected = self.memo_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请选择要删除的备忘录！")
            return

        if messagebox.askyesno("确认", "确定要删除选中的备忘录吗？"):
            for item in selected:
                values = self.memo_tree.item(item)["values"]
                content = values[0]
                remind_time = values[1]
                # 根据内容和时间找到对应的备忘录
                for i, memo in enumerate(self.memos):
                    display_content = memo["content"][:30] + "..." if len(memo["content"]) > 30 else memo["content"]
                    if display_content == content and memo["remind_time"] == remind_time:
                        self.memos.pop(i)
                        break

            self.save_memos()
            self.refresh_memo_list()
            self.update_status()
            messagebox.showinfo("成功", "备忘录删除成功！")

    def on_memo_double_click(self, event):
        """双击备忘录事件"""
        selected = self.memo_tree.selection()
        if selected:
            item = selected[0]
            values = self.memo_tree.item(item)["values"]
            content = values[0]
            remind_time = values[1]
            status = values[2]

            # 找到完整的备忘录内容
            full_content = content
            for memo in self.memos:
                display_content = memo["content"][:30] + "..." if len(memo["content"]) > 30 else memo["content"]
                if display_content == content and memo["remind_time"] == remind_time:
                    full_content = memo["content"]
                    break

            # 显示详细信息
            self.show_memo_detail(full_content, remind_time, status)

    def show_memo_detail(self, content, remind_time, status):
        """显示备忘录详情"""
        detail_window = tk.Toplevel(self.root)
        detail_window.title("📝 备忘录详情")
        detail_window.geometry("400x300")
        detail_window.resizable(False, False)
        detail_window.transient(self.root)
        detail_window.grab_set()

        main_frame = ttk.Frame(detail_window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        ttk.Label(main_frame, text="📝 备忘录详情",
                 font=("Microsoft YaHei", 14, "bold")).pack(pady=(0, 20))

        # 内容
        content_frame = ttk.LabelFrame(main_frame, text="备忘内容", padding="10")
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        content_text = tk.Text(content_frame, height=6, width=40,
                              font=("Microsoft YaHei", 10),
                              wrap=tk.WORD, relief=tk.SOLID, bd=1)
        content_text.pack(fill=tk.BOTH, expand=True)
        content_text.insert("1.0", content)
        content_text.config(state=tk.DISABLED)

        # 信息
        info_frame = ttk.Frame(main_frame)
        info_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(info_frame, text=f"⏰ 提醒时间: {remind_time}",
                 font=("Microsoft YaHei", 10)).pack(anchor=tk.W, pady=2)
        ttk.Label(info_frame, text=f"📊 状态: {status}",
                 font=("Microsoft YaHei", 10)).pack(anchor=tk.W, pady=2)

        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        ttk.Button(button_frame, text="关闭",
                  command=detail_window.destroy).pack(side=tk.RIGHT)

    def refresh_memo_list(self):
        """刷新备忘录列表"""
        # 清空现有项目
        for item in self.memo_tree.get_children():
            self.memo_tree.delete(item)

        # 按时间排序
        sorted_memos = sorted(self.memos, key=lambda x: x["remind_time"])

        # 添加备忘录到列表
        for memo in sorted_memos:
            # 检查是否过期
            remind_time = datetime.strptime(memo["remind_time"], "%Y-%m-%d %H:%M")
            if remind_time <= datetime.now() and memo["status"] == "待提醒":
                memo["status"] = "已过期"

            # 设置状态颜色标记
            status_icon = {
                "待提醒": "⏳",
                "已提醒": "✅",
                "已过期": "❌"
            }.get(memo["status"], "❓")

            display_content = memo["content"][:30] + "..." if len(memo["content"]) > 30 else memo["content"]

            item = self.memo_tree.insert("", tk.END, values=(
                display_content,
                memo["remind_time"],
                f"{status_icon} {memo['status']}"
            ))

            # 根据状态设置不同的标签颜色
            if memo["status"] == "已过期":
                self.memo_tree.set(item, "status", f"❌ {memo['status']}")
            elif memo["status"] == "已提醒":
                self.memo_tree.set(item, "status", f"✅ {memo['status']}")

    def load_memos(self):
        """加载备忘录数据"""
        try:
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"加载数据失败: {e}")
        return []

    def save_memos(self):
        """保存备忘录数据"""
        try:
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(self.memos, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存数据失败: {e}")

    def start_reminder_thread(self):
        """启动提醒线程"""
        self.reminder_thread = threading.Thread(target=self.reminder_loop, daemon=True)
        self.reminder_thread.start()

    def reminder_loop(self):
        """提醒循环"""
        while True:
            try:
                current_time = datetime.now()
                for memo in self.memos:
                    if memo["status"] == "待提醒":
                        remind_time = datetime.strptime(memo["remind_time"], "%Y-%m-%d %H:%M")
                        if current_time >= remind_time:
                            self.show_reminder(memo)
                            memo["status"] = "已提醒"
                            self.save_memos()
                            # 更新UI
                            self.root.after(0, self.refresh_memo_list)
                            self.root.after(0, self.update_status)

                time.sleep(30)  # 每30秒检查一次
            except Exception as e:
                print(f"提醒线程错误: {e}")
                time.sleep(60)

    def show_reminder(self, memo):
        """显示提醒"""
        # 播放提醒声音
        try:
            winsound.MessageBeep(winsound.MB_ICONEXCLAMATION)
            # 连续播放3次
            for _ in range(2):
                time.sleep(0.5)
                winsound.MessageBeep(winsound.MB_ICONEXCLAMATION)
        except:
            pass

        # 显示提醒窗口
        self.root.after(0, lambda: self.show_reminder_window(memo))

    def show_reminder_window(self, memo):
        """显示提醒窗口"""
        reminder_window = tk.Toplevel(self.root)
        reminder_window.title("⏰ 备忘提醒")
        reminder_window.geometry("400x250")
        reminder_window.resizable(False, False)
        reminder_window.attributes("-topmost", True)

        # 居中显示
        reminder_window.transient(self.root)
        reminder_window.grab_set()

        # 设置窗口位置（屏幕中央）
        reminder_window.update_idletasks()
        x = (reminder_window.winfo_screenwidth() // 2) - (400 // 2)
        y = (reminder_window.winfo_screenheight() // 2) - (250 // 2)
        reminder_window.geometry(f"400x250+{x}+{y}")

        main_frame = ttk.Frame(reminder_window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 提醒图标和标题
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 20))

        ttk.Label(title_frame, text="⏰", font=("Microsoft YaHei", 24)).pack(side=tk.LEFT)
        ttk.Label(title_frame, text="备忘提醒",
                 font=("Microsoft YaHei", 16, "bold")).pack(side=tk.LEFT, padx=(15, 0))

        # 时间信息
        time_label = ttk.Label(title_frame, text=f"提醒时间: {memo['remind_time']}",
                              font=("Microsoft YaHei", 9))
        time_label.pack(side=tk.RIGHT)

        # 备忘内容
        content_frame = ttk.LabelFrame(main_frame, text="📝 备忘内容", padding="15")
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        content_text = tk.Text(content_frame, height=5, width=40,
                              font=("Microsoft YaHei", 11),
                              wrap=tk.WORD, relief=tk.SOLID, bd=1)
        content_text.pack(fill=tk.BOTH, expand=True)
        content_text.insert("1.0", memo["content"])
        content_text.config(state=tk.DISABLED)

        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        ttk.Button(button_frame, text="✅ 知道了",
                  command=reminder_window.destroy).pack(side=tk.RIGHT, padx=(10, 0))
        ttk.Button(button_frame, text="🔔 稍后提醒",
                  command=lambda: self.snooze_reminder(memo, reminder_window)).pack(side=tk.RIGHT)

    def snooze_reminder(self, memo, window):
        """稍后提醒"""
        window.destroy()

        # 设置10分钟后再次提醒
        from datetime import timedelta
        new_time = datetime.now() + timedelta(minutes=10)
        memo["remind_time"] = new_time.strftime("%Y-%m-%d %H:%M")
        memo["status"] = "待提醒"

        self.save_memos()
        self.refresh_memo_list()
        self.update_status()

        messagebox.showinfo("稍后提醒", "已设置10分钟后再次提醒")

    def create_tray_icon(self):
        """创建系统托盘图标"""
        if not TRAY_AVAILABLE:
            return None

        # 创建图标图像
        image = Image.new('RGB', (64, 64), color='white')
        draw = ImageDraw.Draw(image)
        draw.rectangle([16, 16, 48, 48], fill='blue')
        draw.text((20, 20), "📝", fill='white')

        # 创建菜单
        menu = pystray.Menu(
            pystray.MenuItem("显示窗口", self.show_window),
            pystray.MenuItem("添加备忘", self.show_add_memo_dialog),
            pystray.Menu.SEPARATOR,
            pystray.MenuItem("退出", self.quit_app)
        )

        return pystray.Icon("备忘录", image, "备忘录小工具", menu)

    def minimize_to_tray(self):
        """最小化到系统托盘"""
        if TRAY_AVAILABLE:
            self.root.withdraw()
            if not self.tray_icon:
                self.tray_icon = self.create_tray_icon()
                threading.Thread(target=self.tray_icon.run, daemon=True).start()
        else:
            self.hide_window()

    def hide_window(self):
        """隐藏窗口"""
        self.root.withdraw()
        messagebox.showinfo("提示", "程序已隐藏，可通过任务栏恢复")

    def show_window(self, icon=None, item=None):
        """显示窗口"""
        self.root.deiconify()
        self.root.lift()
        self.root.focus_force()

    def show_add_memo_dialog(self, icon=None, item=None):
        """显示添加备忘对话框"""
        self.show_window()
        self.memo_text.focus_set()

    def quit_app(self, icon=None, item=None):
        """退出应用"""
        if self.tray_icon:
            self.tray_icon.stop()
        self.root.quit()

    def run(self):
        """运行应用程序"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

    def on_closing(self):
        """关闭程序时的处理"""
        if TRAY_AVAILABLE:
            # 如果支持托盘，询问是否最小化到托盘
            result = messagebox.askyesnocancel("退出确认",
                                             "是否最小化到系统托盘？\n\n"
                                             "是 - 最小化到托盘\n"
                                             "否 - 完全退出程序\n"
                                             "取消 - 继续运行")
            if result is True:
                self.minimize_to_tray()
            elif result is False:
                self.quit_app()
        else:
            if messagebox.askokcancel("退出", "确定要退出备忘录程序吗？"):
                self.root.destroy()


def main():
    """主函数"""
    app = MemoApp()
    app.run()


if __name__ == "__main__":
    main()
