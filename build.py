#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
备忘录小工具自动化打包脚本
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path
import time

def check_pyinstaller():
    """检查PyInstaller是否已安装"""
    try:
        import PyInstaller
        print(f"✅ PyInstaller已安装，版本: {PyInstaller.__version__}")
        return True
    except ImportError:
        print("❌ PyInstaller未安装")
        return False

def install_dependencies():
    """安装打包依赖"""
    print("📦 安装打包依赖...")
    
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "build_requirements.txt"], 
                      check=True, capture_output=True, text=True)
        print("✅ 依赖安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        print("请手动运行: pip install -r build_requirements.txt")
        return False

def create_icon():
    """创建程序图标"""
    print("🎨 创建程序图标...")
    
    try:
        subprocess.run([sys.executable, "create_icon.py"], check=True)
        return True
    except subprocess.CalledProcessError:
        print("⚠️ 图标创建失败，将使用默认图标")
        return False

def clean_build_dirs():
    """清理构建目录"""
    print("🧹 清理旧的构建文件...")
    
    dirs_to_clean = ['build', 'dist', '__pycache__']
    files_to_clean = ['*.pyc']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"  删除目录: {dir_name}")
    
    # 清理pyc文件
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.pyc'):
                os.remove(os.path.join(root, file))

def build_basic_version():
    """打包基础版本"""
    print("\n🔨 开始打包基础版本...")
    
    try:
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--clean",
            "--noconfirm", 
            "memo.spec"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 基础版本打包成功")
            return True
        else:
            print(f"❌ 基础版本打包失败:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 打包过程出错: {e}")
        return False

def build_enhanced_version():
    """打包增强版本"""
    print("\n🔨 开始打包增强版本...")
    
    # 检查是否有增强版依赖
    try:
        import pystray
        import PIL
        has_enhanced_deps = True
    except ImportError:
        print("⚠️ 缺少增强版依赖，跳过增强版打包")
        print("如需打包增强版，请运行: pip install pystray pillow")
        return False
    
    try:
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--clean",
            "--noconfirm",
            "memo_enhanced.spec"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 增强版本打包成功")
            return True
        else:
            print(f"❌ 增强版本打包失败:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 打包过程出错: {e}")
        return False

def create_portable_package():
    """创建便携版包"""
    print("\n📦 创建便携版包...")
    
    portable_dir = Path("便携版")
    if portable_dir.exists():
        shutil.rmtree(portable_dir)
    
    portable_dir.mkdir()
    
    # 复制exe文件
    dist_dir = Path("dist")
    if dist_dir.exists():
        for exe_file in dist_dir.glob("*.exe"):
            shutil.copy2(exe_file, portable_dir)
            print(f"  复制: {exe_file.name}")
    
    # 复制文档文件
    docs = ["README.md", "安装指南.md"]
    for doc in docs:
        if os.path.exists(doc):
            shutil.copy2(doc, portable_dir)
            print(f"  复制: {doc}")
    
    # 创建开机自启动设置exe
    try:
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--onefile",
            "--console",
            "--name", "开机自启动设置",
            "setup_autostart.py"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            setup_exe = Path("dist/开机自启动设置.exe")
            if setup_exe.exists():
                shutil.copy2(setup_exe, portable_dir)
                print("  复制: 开机自启动设置.exe")
        
    except Exception as e:
        print(f"⚠️ 开机自启动设置打包失败: {e}")
    
    # 创建使用说明
    usage_text = """# 📝 备忘录小工具 - 便携版

## 🚀 快速开始

1. **运行程序**: 双击 `备忘录.exe` 启动基础版本
2. **增强版本**: 如果有 `备忘录增强版.exe`，双击启动增强版本
3. **开机自启**: 双击 `开机自启动设置.exe` 设置开机自启动

## ✨ 版本说明

- **基础版**: 使用系统标准库，体积小，兼容性好
- **增强版**: 支持系统托盘功能，需要额外依赖

## 📋 功能特点

- ✅ 多任务备忘录管理
- ⏰ 精确的日期时间提醒
- 🔊 声音提醒功能
- 🎨 极简UI设计
- 💾 数据自动保存
- 🚀 开机自启动支持

## ⚠️ 注意事项

1. 程序数据保存在 `memo_data.json` 文件中
2. 首次运行可能被杀毒软件拦截，请添加信任
3. 开机自启动需要管理员权限

## 📞 技术支持

如有问题，请检查：
- Windows版本兼容性
- 杀毒软件设置
- 用户权限设置

---
🎉 感谢使用备忘录小工具！
"""
    
    with open(portable_dir / "使用说明.txt", "w", encoding="utf-8") as f:
        f.write(usage_text)
    
    print("✅ 便携版包创建完成")
    return True

def show_build_summary():
    """显示构建总结"""
    print("\n" + "="*60)
    print("📊 构建总结")
    print("="*60)
    
    dist_dir = Path("dist")
    portable_dir = Path("便携版")
    
    if dist_dir.exists():
        print("\n📁 dist目录文件:")
        for file in dist_dir.iterdir():
            if file.is_file():
                size = file.stat().st_size / (1024 * 1024)  # MB
                print(f"  {file.name} ({size:.1f} MB)")
    
    if portable_dir.exists():
        print("\n📦 便携版目录文件:")
        for file in portable_dir.iterdir():
            if file.is_file():
                size = file.stat().st_size / (1024 * 1024) if file.suffix == '.exe' else file.stat().st_size / 1024  # MB or KB
                unit = "MB" if file.suffix == '.exe' else "KB"
                print(f"  {file.name} ({size:.1f} {unit})")
    
    print(f"\n🎉 打包完成！")
    print(f"📂 可执行文件位置: {dist_dir.absolute()}")
    print(f"📦 便携版位置: {portable_dir.absolute()}")

def main():
    """主函数"""
    print("🔧 备忘录小工具 - 自动化打包脚本")
    print("="*60)
    
    start_time = time.time()
    
    # 检查PyInstaller
    if not check_pyinstaller():
        print("正在安装PyInstaller...")
        if not install_dependencies():
            return False
    
    # 创建图标
    create_icon()
    
    # 清理构建目录
    clean_build_dirs()
    
    # 打包基础版本
    basic_success = build_basic_version()
    
    # 打包增强版本
    enhanced_success = build_enhanced_version()
    
    # 创建便携版包
    if basic_success or enhanced_success:
        create_portable_package()
    
    # 显示总结
    show_build_summary()
    
    end_time = time.time()
    print(f"\n⏱️ 总耗时: {end_time - start_time:.1f} 秒")
    
    return basic_success or enhanced_success

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
