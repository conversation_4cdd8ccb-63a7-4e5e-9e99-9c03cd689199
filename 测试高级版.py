#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能备忘录高级版 - 功能测试脚本
测试4个新增功能的实现
"""

import os
import sys
import json
from datetime import datetime, timedelta

def test_advanced_features():
    """测试高级版新功能"""
    print("🔮 智能备忘录高级版 - 功能测试")
    print("=" * 50)
    
    # 测试1: 自定义声音功能
    print("\n🔊 测试1: 自定义声音功能")
    test_custom_sound_feature()
    
    # 测试2: 任务删除功能
    print("\n🗑️ 测试2: 任务删除功能")
    test_task_deletion_feature()
    
    # 测试3: 精确时间功能
    print("\n⏰ 测试3: 精确时间功能")
    test_precise_time_feature()
    
    # 测试4: 科技感UI功能
    print("\n🎨 测试4: 科技感UI功能")
    test_tech_ui_feature()
    
    print("\n" + "=" * 50)
    print("📊 测试总结")
    print("=" * 50)

def test_custom_sound_feature():
    """测试自定义声音功能"""
    print("检查自定义声音相关功能...")
    
    # 检查声音目录
    sounds_dir = "custom_sounds"
    if not os.path.exists(sounds_dir):
        os.makedirs(sounds_dir)
        print(f"✅ 创建声音目录: {sounds_dir}")
    else:
        print(f"✅ 声音目录已存在: {sounds_dir}")
    
    # 检查pygame依赖
    try:
        import pygame
        print("✅ pygame音频引擎可用")
        
        # 测试pygame初始化
        pygame.mixer.init()
        print("✅ pygame音频系统初始化成功")
        pygame.mixer.quit()
        
    except ImportError:
        print("❌ pygame未安装，自定义声音功能不可用")
        print("   请运行: pip install pygame")
    except Exception as e:
        print(f"⚠️ pygame初始化异常: {e}")
    
    # 检查配置文件结构
    config_file = "app_config.json"
    default_config = {
        "default_sound": "system",
        "custom_sounds": [],
        "reminder_interval": 30,
        "auto_refresh": True
    }
    
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, ensure_ascii=False, indent=2)
        print(f"✅ 配置文件结构测试通过: {config_file}")
    except Exception as e:
        print(f"❌ 配置文件测试失败: {e}")

def test_task_deletion_feature():
    """测试任务删除功能"""
    print("检查任务删除相关功能...")
    
    # 创建测试数据
    test_memos = [
        {
            "id": 1001,
            "content": "测试任务1 - 待提醒",
            "remind_time": (datetime.now() + timedelta(hours=1)).strftime("%Y-%m-%d %H:%M"),
            "sound": "system",
            "status": "待提醒",
            "created_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        },
        {
            "id": 1002,
            "content": "测试任务2 - 已提醒",
            "remind_time": (datetime.now() - timedelta(hours=1)).strftime("%Y-%m-%d %H:%M"),
            "sound": "system",
            "status": "已提醒",
            "created_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        },
        {
            "id": 1003,
            "content": "测试任务3 - 已过期",
            "remind_time": (datetime.now() - timedelta(hours=2)).strftime("%Y-%m-%d %H:%M"),
            "sound": "system",
            "status": "已过期",
            "created_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
    ]
    
    # 测试数据文件操作
    data_file = "memo_data_test.json"
    try:
        with open(data_file, 'w', encoding='utf-8') as f:
            json.dump(test_memos, f, ensure_ascii=False, indent=2)
        print(f"✅ 测试数据创建成功: {data_file}")
        
        # 测试删除操作
        # 删除已完成任务（已提醒和已过期）
        active_memos = [m for m in test_memos if m["status"] == "待提醒"]
        completed_count = len(test_memos) - len(active_memos)
        
        print(f"✅ 任务分类测试: 总计{len(test_memos)}个，待提醒{len(active_memos)}个，已完成{completed_count}个")
        
        # 保存删除后的数据
        with open(data_file, 'w', encoding='utf-8') as f:
            json.dump(active_memos, f, ensure_ascii=False, indent=2)
        print(f"✅ 批量删除测试通过: 删除{completed_count}个已完成任务")
        
        # 清理测试文件
        os.remove(data_file)
        print(f"✅ 测试文件清理完成")
        
    except Exception as e:
        print(f"❌ 任务删除功能测试失败: {e}")

def test_precise_time_feature():
    """测试精确时间功能"""
    print("检查精确时间相关功能...")
    
    # 测试时间精度
    current_time = datetime.now()
    print(f"✅ 当前时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试分钟级精度
    minute_precision = current_time.replace(second=0, microsecond=0)
    print(f"✅ 分钟精度: {minute_precision.strftime('%Y-%m-%d %H:%M')}")
    
    # 测试快捷时间设置
    quick_times = [
        ("5分钟后", 5/60),
        ("30分钟后", 0.5),
        ("1小时后", 1),
        ("明天", 24),
        ("一周后", 24*7)
    ]
    
    print("✅ 快捷时间设置测试:")
    for name, hours in quick_times:
        target_time = current_time + timedelta(hours=hours)
        print(f"   {name}: {target_time.strftime('%Y-%m-%d %H:%M')}")
    
    # 测试时间验证
    past_time = current_time - timedelta(minutes=30)
    future_time = current_time + timedelta(minutes=30)
    
    print(f"✅ 时间验证测试:")
    print(f"   过去时间 {past_time.strftime('%H:%M')} <= 当前时间: {past_time <= current_time}")
    print(f"   未来时间 {future_time.strftime('%H:%M')} > 当前时间: {future_time > current_time}")

def test_tech_ui_feature():
    """测试科技感UI功能"""
    print("检查科技感UI相关功能...")
    
    # 测试颜色配置
    ui_colors = {
        "bg_color": "#1a1a1a",           # 深黑背景
        "secondary_bg": "#2d2d2d",       # 次级背景
        "accent_color": "#00d4ff",       # 科技蓝
        "accent_hover": "#00b8e6",       # 悬停蓝
        "text_color": "#ffffff",         # 白色文字
        "text_secondary": "#b0b0b0",     # 次级文字
        "border_color": "#404040",       # 边框色
        "success_color": "#00ff88",      # 成功绿
        "warning_color": "#ff6b35",      # 警告橙
        "error_color": "#ff3366",        # 错误红
    }
    
    print("✅ 科技感配色方案:")
    for name, color in ui_colors.items():
        print(f"   {name}: {color}")
    
    # 测试字体配置
    fonts = {
        "main_font": ("Segoe UI", 10),
        "title_font": ("Segoe UI", 16, "bold"),
        "secondary_font": ("Segoe UI", 9),
        "large_font": ("Segoe UI", 18, "bold")
    }
    
    print("✅ 字体配置:")
    for name, font in fonts.items():
        print(f"   {name}: {font}")
    
    # 测试状态图标
    status_icons = {
        "待提醒": "⏳",
        "已提醒": "✅", 
        "已过期": "❌"
    }
    
    print("✅ 状态图标:")
    for status, icon in status_icons.items():
        print(f"   {status}: {icon}")
    
    # 测试tkinter可用性
    try:
        import tkinter as tk
        from tkinter import ttk
        print("✅ tkinter GUI库可用")
        
        # 测试样式主题
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        style = ttk.Style()
        available_themes = style.theme_names()
        print(f"✅ 可用主题: {', '.join(available_themes)}")
        
        root.destroy()
        
    except Exception as e:
        print(f"❌ tkinter测试失败: {e}")

def check_dependencies():
    """检查依赖包"""
    print("\n📦 检查依赖包...")
    
    dependencies = [
        ("tkinter", "GUI界面库"),
        ("pygame", "音频播放库"),
        ("pystray", "系统托盘库（可选）"),
        ("PIL", "图像处理库（可选）")
    ]
    
    for module, description in dependencies:
        try:
            __import__(module)
            print(f"✅ {module} - {description}")
        except ImportError:
            if module in ["pystray", "PIL"]:
                print(f"⚠️ {module} - {description} (可选，未安装)")
            else:
                print(f"❌ {module} - {description} (必需，未安装)")

def main():
    """主函数"""
    print("🔧 智能备忘录高级版 - 功能测试程序")
    print("=" * 60)
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"🐍 Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 7):
        print("❌ Python版本过低，需要3.7或更高版本")
        return False
    else:
        print("✅ Python版本符合要求")
    
    # 检查依赖
    check_dependencies()
    
    # 运行功能测试
    test_advanced_features()
    
    print("\n🎉 高级版功能测试完成！")
    print("\n🚀 启动建议:")
    print("1. 运行: python bwl_advanced.py")
    print("2. 或双击: 启动高级版.bat")
    print("3. 打包: 双击 打包高级版.bat")
    
    return True

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
