#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
备忘录小工具 - 高级版
新增功能：
1. 自定义声音上传
2. 任务自由删除
3. 时间精确到分钟
4. 科技感金属简约风格UI
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime, timedelta
import json
import threading
import time
import os
import sys
import winsound
from pathlib import Path
import pygame

try:
    import pystray
    from PIL import Image, ImageDraw
    TRAY_AVAILABLE = True
except ImportError:
    TRAY_AVAILABLE = False

class MemoApp:
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.data_file = "memo_data.json"
        self.config_file = "app_config.json"
        self.sounds_dir = "custom_sounds"
        self.memos = self.load_memos()
        self.config = self.load_config()
        self.setup_sounds_dir()
        self.init_pygame()
        self.setup_ui()
        self.start_reminder_thread()
        self.tray_icon = None

    def setup_window(self):
        """设置主窗口 - 科技感金属风格"""
        self.root.title("🔮 智能备忘录")
        self.root.geometry("520x680")
        self.root.resizable(False, False)

        # 科技感金属风格配色
        self.bg_color = "#1a1a1a"           # 深黑背景
        self.secondary_bg = "#2d2d2d"       # 次级背景
        self.accent_color = "#00d4ff"       # 科技蓝
        self.accent_hover = "#00b8e6"       # 悬停蓝
        self.text_color = "#ffffff"         # 白色文字
        self.text_secondary = "#b0b0b0"     # 次级文字
        self.border_color = "#404040"       # 边框色
        self.success_color = "#00ff88"      # 成功绿
        self.warning_color = "#ff6b35"      # 警告橙
        self.error_color = "#ff3366"        # 错误红

        self.root.configure(bg=self.bg_color)

        # 设置现代化样式
        self.setup_styles()

        # 设置窗口图标
        try:
            self.root.iconbitmap(default='memo.ico')
        except:
            pass

    def setup_styles(self):
        """设置科技感样式主题"""
        style = ttk.Style()
        style.theme_use('clam')

        # 配置主要样式
        style.configure('Tech.TFrame',
                       background=self.bg_color,
                       relief='flat',
                       borderwidth=0)

        style.configure('TechCard.TFrame',
                       background=self.secondary_bg,
                       relief='solid',
                       borderwidth=1)

        style.configure('Tech.TLabel',
                       background=self.bg_color,
                       foreground=self.text_color,
                       font=('Segoe UI', 10))

        style.configure('TechTitle.TLabel',
                       background=self.bg_color,
                       foreground=self.accent_color,
                       font=('Segoe UI', 16, 'bold'))

        style.configure('TechSecondary.TLabel',
                       background=self.secondary_bg,
                       foreground=self.text_secondary,
                       font=('Segoe UI', 9))

        style.configure('Tech.TButton',
                       background=self.accent_color,
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none',
                       font=('Segoe UI', 9, 'bold'),
                       padding=(12, 8))

        style.map('Tech.TButton',
                 background=[('active', self.accent_hover),
                           ('pressed', '#0099cc')])

        style.configure('TechDanger.TButton',
                       background=self.error_color,
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none',
                       font=('Segoe UI', 9, 'bold'),
                       padding=(10, 6))

        style.map('TechDanger.TButton',
                 background=[('active', '#ff1a4d'),
                           ('pressed', '#cc0029')])

        style.configure('TechSuccess.TButton',
                       background=self.success_color,
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none',
                       font=('Segoe UI', 9, 'bold'),
                       padding=(10, 6))

        # Treeview样式
        style.configure('Tech.Treeview',
                       background=self.secondary_bg,
                       foreground=self.text_color,
                       fieldbackground=self.secondary_bg,
                       borderwidth=0,
                       font=('Segoe UI', 9))

        style.configure('Tech.Treeview.Heading',
                       background=self.bg_color,
                       foreground=self.accent_color,
                       borderwidth=1,
                       relief='solid',
                       font=('Segoe UI', 10, 'bold'))

        # LabelFrame样式
        style.configure('Tech.TLabelframe',
                       background=self.bg_color,
                       borderwidth=2,
                       relief='solid')

        style.configure('Tech.TLabelframe.Label',
                       background=self.bg_color,
                       foreground=self.accent_color,
                       font=('Segoe UI', 11, 'bold'))

        # Spinbox样式
        style.configure('Tech.TSpinbox',
                       fieldbackground=self.secondary_bg,
                       background=self.secondary_bg,
                       foreground=self.text_color,
                       borderwidth=1,
                       insertcolor=self.accent_color)

    def setup_sounds_dir(self):
        """设置自定义声音目录"""
        if not os.path.exists(self.sounds_dir):
            os.makedirs(self.sounds_dir)

    def init_pygame(self):
        """初始化pygame音频系统"""
        try:
            pygame.mixer.init()
            self.pygame_available = True
        except:
            self.pygame_available = False
            print("pygame音频系统初始化失败，将使用系统默认声音")

    def load_config(self):
        """加载应用配置"""
        default_config = {
            "default_sound": "system",
            "custom_sounds": [],
            "reminder_interval": 30,
            "auto_refresh": True
        }

        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # 合并默认配置
                    for key, value in default_config.items():
                        if key not in config:
                            config[key] = value
                    return config
        except Exception as e:
            print(f"加载配置失败: {e}")

        return default_config

    def save_config(self):
        """保存应用配置"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存配置失败: {e}")

    def setup_ui(self):
        """设置用户界面"""
        # 主容器
        main_frame = ttk.Frame(self.root, style='Tech.TFrame', padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题区域
        self.setup_header(main_frame)

        # 添加备忘录区域
        self.setup_add_memo_section(main_frame)

        # 备忘录列表区域
        self.setup_memo_list_section(main_frame)

        # 底部控制区域
        self.setup_bottom_controls(main_frame)

        # 更新状态
        self.update_status()

    def setup_header(self, parent):
        """设置标题区域"""
        header_frame = ttk.Frame(parent, style='Tech.TFrame')
        header_frame.pack(fill=tk.X, pady=(0, 25))

        # 主标题
        title_label = ttk.Label(header_frame,
                               text="🔮 智能备忘录",
                               style='TechTitle.TLabel')
        title_label.pack(side=tk.LEFT)

        # 状态信息
        status_frame = ttk.Frame(header_frame, style='Tech.TFrame')
        status_frame.pack(side=tk.RIGHT)

        self.status_label = ttk.Label(status_frame,
                                     text="",
                                     style='Tech.TLabel',
                                     font=('Segoe UI', 9))
        self.status_label.pack()

        # 分隔线
        separator = tk.Frame(parent, height=2, bg=self.accent_color)
        separator.pack(fill=tk.X, pady=(0, 20))

    def setup_add_memo_section(self, parent):
        """设置添加备忘录区域"""
        add_frame = ttk.LabelFrame(parent, text="✨ 创建新任务",
                                  style='Tech.TLabelframe', padding="20")
        add_frame.pack(fill=tk.X, pady=(0, 20))

        # 备忘内容输入
        content_label = ttk.Label(add_frame, text="📝 任务内容", style='Tech.TLabel')
        content_label.pack(anchor=tk.W, pady=(0, 8))

        self.memo_text = tk.Text(add_frame, height=3, width=50,
                                font=('Segoe UI', 10),
                                bg=self.secondary_bg,
                                fg=self.text_color,
                                insertbackground=self.accent_color,
                                relief='solid',
                                bd=1,
                                selectbackground=self.accent_color,
                                selectforeground='white')
        self.memo_text.pack(fill=tk.X, pady=(0, 15))

        # 时间设置区域
        time_frame = ttk.Frame(add_frame, style='Tech.TFrame')
        time_frame.pack(fill=tk.X, pady=(0, 15))

        # 日期设置
        date_frame = ttk.Frame(time_frame, style='Tech.TFrame')
        date_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

        date_label = ttk.Label(date_frame, text="📅 提醒日期", style='Tech.TLabel')
        date_label.pack(anchor=tk.W, pady=(0, 5))

        date_input_frame = ttk.Frame(date_frame, style='Tech.TFrame')
        date_input_frame.pack(fill=tk.X)

        # 年月日输入 - 精确到分钟
        now = datetime.now()
        self.year_var = tk.StringVar(value=str(now.year))
        self.month_var = tk.StringVar(value=str(now.month))
        self.day_var = tk.StringVar(value=str(now.day))
        self.hour_var = tk.StringVar(value=str(now.hour))
        self.minute_var = tk.StringVar(value=str(now.minute))

        # 年
        year_spin = ttk.Spinbox(date_input_frame, from_=2024, to=2030,
                               width=6, textvariable=self.year_var,
                               style='Tech.TSpinbox')
        year_spin.pack(side=tk.LEFT, padx=(0, 5))

        ttk.Label(date_input_frame, text="年", style='Tech.TLabel').pack(side=tk.LEFT, padx=(0, 10))

        # 月
        month_spin = ttk.Spinbox(date_input_frame, from_=1, to=12,
                                width=4, textvariable=self.month_var,
                                style='Tech.TSpinbox')
        month_spin.pack(side=tk.LEFT, padx=(0, 5))

        ttk.Label(date_input_frame, text="月", style='Tech.TLabel').pack(side=tk.LEFT, padx=(0, 10))

        # 日
        day_spin = ttk.Spinbox(date_input_frame, from_=1, to=31,
                              width=4, textvariable=self.day_var,
                              style='Tech.TSpinbox')
        day_spin.pack(side=tk.LEFT, padx=(0, 5))

        ttk.Label(date_input_frame, text="日", style='Tech.TLabel').pack(side=tk.LEFT)

        # 时间设置
        time_detail_frame = ttk.Frame(time_frame, style='Tech.TFrame')
        time_detail_frame.pack(side=tk.RIGHT, padx=(30, 0))

        time_label = ttk.Label(time_detail_frame, text="🕐 提醒时间", style='Tech.TLabel')
        time_label.pack(anchor=tk.W, pady=(0, 5))

        time_input_frame = ttk.Frame(time_detail_frame, style='Tech.TFrame')
        time_input_frame.pack()

        # 时
        hour_spin = ttk.Spinbox(time_input_frame, from_=0, to=23,
                               width=4, textvariable=self.hour_var,
                               style='Tech.TSpinbox')
        hour_spin.pack(side=tk.LEFT, padx=(0, 5))

        ttk.Label(time_input_frame, text=":", style='Tech.TLabel').pack(side=tk.LEFT, padx=(0, 5))

        # 分
        minute_spin = ttk.Spinbox(time_input_frame, from_=0, to=59,
                                 width=4, textvariable=self.minute_var,
                                 style='Tech.TSpinbox')
        minute_spin.pack(side=tk.LEFT)

        # 快捷时间设置
        quick_frame = ttk.Frame(add_frame, style='Tech.TFrame')
        quick_frame.pack(fill=tk.X, pady=(0, 15))

        quick_label = ttk.Label(quick_frame, text="⚡ 快捷设置", style='Tech.TLabel')
        quick_label.pack(side=tk.LEFT)

        quick_buttons = [
            ("5分钟后", 5/60),
            ("30分钟后", 0.5),
            ("1小时后", 1),
            ("明天", 24),
            ("一周后", 24*7)
        ]

        for text, hours in quick_buttons:
            btn = ttk.Button(quick_frame, text=text,
                           command=lambda h=hours: self.set_quick_time(h),
                           style='Tech.TButton')
            btn.pack(side=tk.LEFT, padx=(10, 5))

        # 声音设置区域
        sound_frame = ttk.Frame(add_frame, style='Tech.TFrame')
        sound_frame.pack(fill=tk.X, pady=(0, 15))

        sound_label = ttk.Label(sound_frame, text="🔊 提醒声音", style='Tech.TLabel')
        sound_label.pack(side=tk.LEFT)

        # 声音选择下拉框
        self.sound_var = tk.StringVar(value=self.config.get("default_sound", "system"))
        self.sound_combo = ttk.Combobox(sound_frame, textvariable=self.sound_var,
                                       state="readonly", width=20)
        self.sound_combo.pack(side=tk.LEFT, padx=(10, 10))

        # 上传声音按钮
        upload_sound_btn = ttk.Button(sound_frame, text="📁 上传声音",
                                     command=self.upload_custom_sound,
                                     style='Tech.TButton')
        upload_sound_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 测试声音按钮
        test_sound_btn = ttk.Button(sound_frame, text="🎵 测试",
                                   command=self.test_selected_sound,
                                   style='Tech.TButton')
        test_sound_btn.pack(side=tk.LEFT)

        # 更新声音列表
        self.update_sound_list()

        # 添加按钮
        add_btn = ttk.Button(add_frame, text="➕ 创建任务",
                           command=self.add_memo,
                           style='Tech.TButton')
        add_btn.pack(pady=(10, 0))

    def setup_memo_list_section(self, parent):
        """设置备忘录列表区域"""
        list_frame = ttk.LabelFrame(parent, text="📋 任务列表",
                                   style='Tech.TLabelframe', padding="15")
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        # 创建Treeview
        columns = ("content", "datetime", "sound", "status", "actions")
        self.memo_tree = ttk.Treeview(list_frame, columns=columns, show="headings",
                                     height=12, style='Tech.Treeview')

        # 设置列标题和宽度
        self.memo_tree.heading("content", text="任务内容")
        self.memo_tree.heading("datetime", text="提醒时间")
        self.memo_tree.heading("sound", text="声音")
        self.memo_tree.heading("status", text="状态")
        self.memo_tree.heading("actions", text="操作")

        self.memo_tree.column("content", width=200)
        self.memo_tree.column("datetime", width=120)
        self.memo_tree.column("sound", width=80)
        self.memo_tree.column("status", width=80)
        self.memo_tree.column("actions", width=60)

        # 滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.memo_tree.yview)
        self.memo_tree.configure(yscrollcommand=scrollbar.set)

        self.memo_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 绑定事件
        self.memo_tree.bind("<Double-1>", self.on_memo_double_click)
        self.memo_tree.bind("<Button-3>", self.show_context_menu)  # 右键菜单

        self.refresh_memo_list()

    def setup_bottom_controls(self, parent):
        """设置底部控制区域"""
        control_frame = ttk.Frame(parent, style='Tech.TFrame')
        control_frame.pack(fill=tk.X)

        # 左侧按钮
        left_frame = ttk.Frame(control_frame, style='Tech.TFrame')
        left_frame.pack(side=tk.LEFT)

        refresh_btn = ttk.Button(left_frame, text="🔄 刷新",
                               command=self.refresh_memo_list,
                               style='Tech.TButton')
        refresh_btn.pack(side=tk.LEFT, padx=(0, 10))

        delete_all_btn = ttk.Button(left_frame, text="🗑️ 清空已完成",
                                   command=self.delete_completed_memos,
                                   style='TechDanger.TButton')
        delete_all_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 右侧按钮
        right_frame = ttk.Frame(control_frame, style='Tech.TFrame')
        right_frame.pack(side=tk.RIGHT)

        settings_btn = ttk.Button(right_frame, text="⚙️ 设置",
                                 command=self.show_settings,
                                 style='Tech.TButton')
        settings_btn.pack(side=tk.RIGHT, padx=(10, 0))

        if TRAY_AVAILABLE:
            minimize_btn = ttk.Button(right_frame, text="📌 最小化",
                                     command=self.minimize_to_tray,
                                     style='Tech.TButton')
            minimize_btn.pack(side=tk.RIGHT, padx=(10, 0))
        else:
            hide_btn = ttk.Button(right_frame, text="🔽 隐藏",
                                 command=self.hide_window,
                                 style='Tech.TButton')
            hide_btn.pack(side=tk.RIGHT, padx=(10, 0))

    def update_sound_list(self):
        """更新声音选择列表"""
        sounds = ["system"]  # 系统默认声音

        # 添加自定义声音
        if os.path.exists(self.sounds_dir):
            for file in os.listdir(self.sounds_dir):
                if file.lower().endswith(('.wav', '.mp3', '.ogg')):
                    sounds.append(file)

        self.sound_combo['values'] = sounds

        # 设置当前选择
        current_sound = self.config.get("default_sound", "system")
        if current_sound in sounds:
            self.sound_var.set(current_sound)
        else:
            self.sound_var.set("system")

    def upload_custom_sound(self):
        """上传自定义声音文件"""
        file_path = filedialog.askopenfilename(
            title="选择声音文件",
            filetypes=[
                ("音频文件", "*.wav *.mp3 *.ogg"),
                ("WAV文件", "*.wav"),
                ("MP3文件", "*.mp3"),
                ("OGG文件", "*.ogg"),
                ("所有文件", "*.*")
            ]
        )

        if file_path:
            try:
                # 复制文件到声音目录
                file_name = os.path.basename(file_path)
                dest_path = os.path.join(self.sounds_dir, file_name)

                # 如果文件已存在，询问是否覆盖
                if os.path.exists(dest_path):
                    if not messagebox.askyesno("文件已存在", f"声音文件 {file_name} 已存在，是否覆盖？"):
                        return

                import shutil
                shutil.copy2(file_path, dest_path)

                # 更新配置
                if file_name not in self.config["custom_sounds"]:
                    self.config["custom_sounds"].append(file_name)
                    self.save_config()

                # 更新声音列表
                self.update_sound_list()
                self.sound_var.set(file_name)

                messagebox.showinfo("成功", f"声音文件 {file_name} 上传成功！")

            except Exception as e:
                messagebox.showerror("错误", f"上传声音文件失败：{e}")

    def test_selected_sound(self):
        """测试选中的声音"""
        sound_name = self.sound_var.get()
        self.play_sound(sound_name)

    def play_sound(self, sound_name):
        """播放指定声音"""
        try:
            if sound_name == "system":
                # 播放系统声音
                winsound.MessageBeep(winsound.MB_ICONEXCLAMATION)
            else:
                # 播放自定义声音
                sound_path = os.path.join(self.sounds_dir, sound_name)
                if os.path.exists(sound_path) and self.pygame_available:
                    pygame.mixer.music.load(sound_path)
                    pygame.mixer.music.play()
                else:
                    # 回退到系统声音
                    winsound.MessageBeep(winsound.MB_ICONEXCLAMATION)
        except Exception as e:
            print(f"播放声音失败: {e}")
            # 回退到系统声音
            try:
                winsound.MessageBeep(winsound.MB_ICONEXCLAMATION)
            except:
                pass

    def set_quick_time(self, hours):
        """设置快捷时间"""
        target_time = datetime.now() + timedelta(hours=hours)
        self.year_var.set(str(target_time.year))
        self.month_var.set(str(target_time.month))
        self.day_var.set(str(target_time.day))
        self.hour_var.set(str(target_time.hour))
        self.minute_var.set(str(target_time.minute))

    def add_memo(self):
        """添加新备忘录"""
        content = self.memo_text.get("1.0", tk.END).strip()
        if not content:
            messagebox.showwarning("警告", "请输入任务内容！")
            return

        try:
            year = int(self.year_var.get())
            month = int(self.month_var.get())
            day = int(self.day_var.get())
            hour = int(self.hour_var.get())
            minute = int(self.minute_var.get())

            remind_time = datetime(year, month, day, hour, minute)

            if remind_time <= datetime.now():
                messagebox.showwarning("警告", "提醒时间不能早于当前时间！")
                return

        except ValueError:
            messagebox.showerror("错误", "请输入正确的日期时间！")
            return

        # 创建新备忘录
        memo = {
            "id": int(time.time() * 1000),  # 使用时间戳作为唯一ID
            "content": content,
            "remind_time": remind_time.strftime("%Y-%m-%d %H:%M"),
            "sound": self.sound_var.get(),
            "status": "待提醒",
            "created_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

        self.memos.append(memo)
        self.save_memos()
        self.refresh_memo_list()
        self.update_status()

        # 清空输入框
        self.memo_text.delete("1.0", tk.END)

        # 更新默认声音设置
        self.config["default_sound"] = self.sound_var.get()
        self.save_config()

        messagebox.showinfo("成功", "任务创建成功！")

    def show_context_menu(self, event):
        """显示右键菜单"""
        item = self.memo_tree.selection()[0] if self.memo_tree.selection() else None
        if not item:
            return

        # 创建右键菜单
        context_menu = tk.Menu(self.root, tearoff=0,
                              bg=self.secondary_bg,
                              fg=self.text_color,
                              activebackground=self.accent_color,
                              activeforeground='white')

        context_menu.add_command(label="📝 查看详情", command=lambda: self.show_memo_detail_from_selection())
        context_menu.add_command(label="✏️ 编辑任务", command=lambda: self.edit_memo_from_selection())
        context_menu.add_separator()
        context_menu.add_command(label="🔊 测试声音", command=lambda: self.test_memo_sound_from_selection())
        context_menu.add_command(label="⏰ 延后提醒", command=lambda: self.snooze_memo_from_selection())
        context_menu.add_separator()
        context_menu.add_command(label="🗑️ 删除任务", command=lambda: self.delete_memo_from_selection())

        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()

    def delete_memo_from_selection(self):
        """从选择中删除备忘录"""
        selected = self.memo_tree.selection()
        if not selected:
            return

        if messagebox.askyesno("确认删除", "确定要删除选中的任务吗？"):
            for item in selected:
                values = self.memo_tree.item(item)["values"]
                memo_id = self.get_memo_id_from_values(values)
                if memo_id:
                    self.memos = [m for m in self.memos if m["id"] != memo_id]

            self.save_memos()
            self.refresh_memo_list()
            self.update_status()
            messagebox.showinfo("成功", "任务删除成功！")

    def get_memo_id_from_values(self, values):
        """从Treeview值中获取备忘录ID"""
        content = values[0]
        remind_time = values[1]

        for memo in self.memos:
            display_content = memo["content"][:30] + "..." if len(memo["content"]) > 30 else memo["content"]
            if display_content == content and memo["remind_time"] == remind_time:
                return memo["id"]
        return None

    def show_memo_detail_from_selection(self):
        """从选择显示备忘录详情"""
        selected = self.memo_tree.selection()
        if not selected:
            return

        values = self.memo_tree.item(selected[0])["values"]
        memo_id = self.get_memo_id_from_values(values)

        if memo_id:
            memo = next((m for m in self.memos if m["id"] == memo_id), None)
            if memo:
                self.show_memo_detail(memo)

    def edit_memo_from_selection(self):
        """从选择编辑备忘录"""
        selected = self.memo_tree.selection()
        if not selected:
            return

        values = self.memo_tree.item(selected[0])["values"]
        memo_id = self.get_memo_id_from_values(values)

        if memo_id:
            memo = next((m for m in self.memos if m["id"] == memo_id), None)
            if memo:
                self.show_edit_dialog(memo)

    def test_memo_sound_from_selection(self):
        """从选择测试备忘录声音"""
        selected = self.memo_tree.selection()
        if not selected:
            return

        values = self.memo_tree.item(selected[0])["values"]
        memo_id = self.get_memo_id_from_values(values)

        if memo_id:
            memo = next((m for m in self.memos if m["id"] == memo_id), None)
            if memo:
                sound_name = memo.get("sound", "system")
                self.play_sound(sound_name)

    def snooze_memo_from_selection(self):
        """从选择延后备忘录"""
        selected = self.memo_tree.selection()
        if not selected:
            return

        values = self.memo_tree.item(selected[0])["values"]
        memo_id = self.get_memo_id_from_values(values)

        if memo_id:
            memo = next((m for m in self.memos if m["id"] == memo_id), None)
            if memo:
                self.snooze_memo(memo)

    def delete_completed_memos(self):
        """删除已完成的备忘录"""
        completed_count = len([m for m in self.memos if m["status"] in ["已提醒", "已过期"]])

        if completed_count == 0:
            messagebox.showinfo("提示", "没有已完成的任务需要清理")
            return

        if messagebox.askyesno("确认清理", f"确定要清理 {completed_count} 个已完成的任务吗？"):
            self.memos = [m for m in self.memos if m["status"] == "待提醒"]
            self.save_memos()
            self.refresh_memo_list()
            self.update_status()
            messagebox.showinfo("成功", f"已清理 {completed_count} 个已完成的任务")

    def show_edit_dialog(self, memo):
        """显示编辑对话框"""
        edit_window = tk.Toplevel(self.root)
        edit_window.title("✏️ 编辑任务")
        edit_window.geometry("450x400")
        edit_window.resizable(False, False)
        edit_window.configure(bg=self.bg_color)
        edit_window.transient(self.root)
        edit_window.grab_set()

        # 居中显示
        edit_window.update_idletasks()
        x = (edit_window.winfo_screenwidth() // 2) - (450 // 2)
        y = (edit_window.winfo_screenheight() // 2) - (400 // 2)
        edit_window.geometry(f"450x400+{x}+{y}")

        main_frame = ttk.Frame(edit_window, style='Tech.TFrame', padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = ttk.Label(main_frame, text="✏️ 编辑任务",
                               style='TechTitle.TLabel')
        title_label.pack(pady=(0, 20))

        # 内容编辑
        content_label = ttk.Label(main_frame, text="📝 任务内容", style='Tech.TLabel')
        content_label.pack(anchor=tk.W, pady=(0, 8))

        content_text = tk.Text(main_frame, height=4, width=45,
                              font=('Segoe UI', 10),
                              bg=self.secondary_bg,
                              fg=self.text_color,
                              insertbackground=self.accent_color,
                              relief='solid', bd=1)
        content_text.pack(fill=tk.X, pady=(0, 15))
        content_text.insert("1.0", memo["content"])

        # 时间编辑
        time_frame = ttk.Frame(main_frame, style='Tech.TFrame')
        time_frame.pack(fill=tk.X, pady=(0, 15))

        time_label = ttk.Label(time_frame, text="⏰ 提醒时间", style='Tech.TLabel')
        time_label.pack(anchor=tk.W, pady=(0, 8))

        # 解析当前时间
        remind_time = datetime.strptime(memo["remind_time"], "%Y-%m-%d %H:%M")

        time_input_frame = ttk.Frame(time_frame, style='Tech.TFrame')
        time_input_frame.pack()

        # 时间输入控件
        edit_year_var = tk.StringVar(value=str(remind_time.year))
        edit_month_var = tk.StringVar(value=str(remind_time.month))
        edit_day_var = tk.StringVar(value=str(remind_time.day))
        edit_hour_var = tk.StringVar(value=str(remind_time.hour))
        edit_minute_var = tk.StringVar(value=str(remind_time.minute))

        # 年月日时分输入
        controls = [
            (edit_year_var, 2024, 2030, 6, "年"),
            (edit_month_var, 1, 12, 4, "月"),
            (edit_day_var, 1, 31, 4, "日"),
            (edit_hour_var, 0, 23, 4, "时"),
            (edit_minute_var, 0, 59, 4, "分")
        ]

        for var, from_, to, width, label in controls:
            spin = ttk.Spinbox(time_input_frame, from_=from_, to=to,
                              width=width, textvariable=var,
                              style='Tech.TSpinbox')
            spin.pack(side=tk.LEFT, padx=(0, 5))
            ttk.Label(time_input_frame, text=label, style='Tech.TLabel').pack(side=tk.LEFT, padx=(0, 10))

        # 声音选择
        sound_frame = ttk.Frame(main_frame, style='Tech.TFrame')
        sound_frame.pack(fill=tk.X, pady=(0, 20))

        sound_label = ttk.Label(sound_frame, text="🔊 提醒声音", style='Tech.TLabel')
        sound_label.pack(anchor=tk.W, pady=(0, 8))

        edit_sound_var = tk.StringVar(value=memo.get("sound", "system"))
        sound_combo = ttk.Combobox(sound_frame, textvariable=edit_sound_var,
                                  state="readonly", width=30)
        sound_combo['values'] = self.sound_combo['values']
        sound_combo.pack(anchor=tk.W)

        # 按钮
        button_frame = ttk.Frame(main_frame, style='Tech.TFrame')
        button_frame.pack(fill=tk.X, pady=(20, 0))

        def save_changes():
            try:
                new_content = content_text.get("1.0", tk.END).strip()
                if not new_content:
                    messagebox.showwarning("警告", "任务内容不能为空！")
                    return

                new_time = datetime(
                    int(edit_year_var.get()),
                    int(edit_month_var.get()),
                    int(edit_day_var.get()),
                    int(edit_hour_var.get()),
                    int(edit_minute_var.get())
                )

                if new_time <= datetime.now():
                    messagebox.showwarning("警告", "提醒时间不能早于当前时间！")
                    return

                # 更新备忘录
                memo["content"] = new_content
                memo["remind_time"] = new_time.strftime("%Y-%m-%d %H:%M")
                memo["sound"] = edit_sound_var.get()
                memo["status"] = "待提醒"  # 重置状态

                self.save_memos()
                self.refresh_memo_list()
                self.update_status()

                edit_window.destroy()
                messagebox.showinfo("成功", "任务更新成功！")

            except ValueError:
                messagebox.showerror("错误", "请输入正确的日期时间！")

        save_btn = ttk.Button(button_frame, text="💾 保存",
                             command=save_changes,
                             style='TechSuccess.TButton')
        save_btn.pack(side=tk.RIGHT, padx=(10, 0))

        cancel_btn = ttk.Button(button_frame, text="❌ 取消",
                               command=edit_window.destroy,
                               style='Tech.TButton')
        cancel_btn.pack(side=tk.RIGHT)

    def show_memo_detail(self, memo):
        """显示备忘录详情"""
        detail_window = tk.Toplevel(self.root)
        detail_window.title("📝 任务详情")
        detail_window.geometry("400x350")
        detail_window.resizable(False, False)
        detail_window.configure(bg=self.bg_color)
        detail_window.transient(self.root)
        detail_window.grab_set()

        # 居中显示
        detail_window.update_idletasks()
        x = (detail_window.winfo_screenwidth() // 2) - (400 // 2)
        y = (detail_window.winfo_screenheight() // 2) - (350 // 2)
        detail_window.geometry(f"400x350+{x}+{y}")

        main_frame = ttk.Frame(detail_window, style='Tech.TFrame', padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = ttk.Label(main_frame, text="📝 任务详情",
                               style='TechTitle.TLabel')
        title_label.pack(pady=(0, 20))

        # 内容显示
        content_frame = ttk.LabelFrame(main_frame, text="任务内容",
                                      style='Tech.TLabelframe', padding="15")
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        content_text = tk.Text(content_frame, height=6, width=40,
                              font=('Segoe UI', 10),
                              bg=self.secondary_bg,
                              fg=self.text_color,
                              wrap=tk.WORD, relief='solid', bd=1)
        content_text.pack(fill=tk.BOTH, expand=True)
        content_text.insert("1.0", memo["content"])
        content_text.config(state=tk.DISABLED)

        # 信息显示
        info_frame = ttk.Frame(main_frame, style='Tech.TFrame')
        info_frame.pack(fill=tk.X, pady=(0, 15))

        info_labels = [
            ("⏰ 提醒时间", memo["remind_time"]),
            ("🔊 提醒声音", memo.get("sound", "system")),
            ("📊 当前状态", memo["status"]),
            ("📅 创建时间", memo.get("created_time", "未知"))
        ]

        for label, value in info_labels:
            info_row = ttk.Frame(info_frame, style='Tech.TFrame')
            info_row.pack(fill=tk.X, pady=2)

            ttk.Label(info_row, text=label, style='Tech.TLabel',
                     font=('Segoe UI', 9, 'bold')).pack(side=tk.LEFT)
            ttk.Label(info_row, text=value, style='Tech.TLabel').pack(side=tk.LEFT, padx=(10, 0))

        # 按钮
        button_frame = ttk.Frame(main_frame, style='Tech.TFrame')
        button_frame.pack(fill=tk.X)

        ttk.Button(button_frame, text="🔊 测试声音",
                  command=lambda: self.play_sound(memo.get("sound", "system")),
                  style='Tech.TButton').pack(side=tk.LEFT)

        ttk.Button(button_frame, text="关闭",
                  command=detail_window.destroy,
                  style='Tech.TButton').pack(side=tk.RIGHT)

    def snooze_memo(self, memo):
        """延后备忘录"""
        snooze_window = tk.Toplevel(self.root)
        snooze_window.title("⏰ 延后提醒")
        snooze_window.geometry("300x200")
        snooze_window.resizable(False, False)
        snooze_window.configure(bg=self.bg_color)
        snooze_window.transient(self.root)
        snooze_window.grab_set()

        # 居中显示
        snooze_window.update_idletasks()
        x = (snooze_window.winfo_screenwidth() // 2) - (300 // 2)
        y = (snooze_window.winfo_screenheight() // 2) - (200 // 2)
        snooze_window.geometry(f"300x200+{x}+{y}")

        main_frame = ttk.Frame(snooze_window, style='Tech.TFrame', padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = ttk.Label(main_frame, text="⏰ 延后提醒",
                               style='TechTitle.TLabel')
        title_label.pack(pady=(0, 20))

        # 延后选项
        snooze_options = [
            ("5分钟后", 5),
            ("15分钟后", 15),
            ("30分钟后", 30),
            ("1小时后", 60),
            ("明天同一时间", 24*60)
        ]

        for text, minutes in snooze_options:
            def snooze_action(m=minutes):
                new_time = datetime.now() + timedelta(minutes=m)
                memo["remind_time"] = new_time.strftime("%Y-%m-%d %H:%M")
                memo["status"] = "待提醒"

                self.save_memos()
                self.refresh_memo_list()
                self.update_status()

                snooze_window.destroy()
                messagebox.showinfo("成功", f"任务已延后到 {new_time.strftime('%Y-%m-%d %H:%M')}")

            btn = ttk.Button(main_frame, text=text,
                           command=snooze_action,
                           style='Tech.TButton')
            btn.pack(fill=tk.X, pady=2)

    def on_memo_double_click(self, event):
        """双击备忘录事件"""
        selected = self.memo_tree.selection()
        if selected:
            self.show_memo_detail_from_selection()

    def refresh_memo_list(self):
        """刷新备忘录列表"""
        # 清空现有项目
        for item in self.memo_tree.get_children():
            self.memo_tree.delete(item)

        # 按时间排序
        sorted_memos = sorted(self.memos, key=lambda x: x["remind_time"])

        # 添加备忘录到列表
        for memo in sorted_memos:
            # 检查是否过期
            remind_time = datetime.strptime(memo["remind_time"], "%Y-%m-%d %H:%M")
            if remind_time <= datetime.now() and memo["status"] == "待提醒":
                memo["status"] = "已过期"

            # 设置状态图标
            status_icons = {
                "待提醒": "⏳",
                "已提醒": "✅",
                "已过期": "❌"
            }

            status_icon = status_icons.get(memo["status"], "❓")
            display_content = memo["content"][:30] + "..." if len(memo["content"]) > 30 else memo["content"]
            sound_display = memo.get("sound", "system")
            if sound_display != "system":
                sound_display = sound_display[:10] + "..." if len(sound_display) > 10 else sound_display

            item = self.memo_tree.insert("", tk.END, values=(
                display_content,
                memo["remind_time"],
                sound_display,
                f"{status_icon} {memo['status']}",
                "🗑️"
            ))

            # 根据状态设置行颜色
            if memo["status"] == "已过期":
                self.memo_tree.set(item, "status", f"❌ {memo['status']}")
            elif memo["status"] == "已提醒":
                self.memo_tree.set(item, "status", f"✅ {memo['status']}")

    def update_status(self):
        """更新状态信息"""
        total = len(self.memos)
        pending = len([m for m in self.memos if m["status"] == "待提醒"])
        completed = len([m for m in self.memos if m["status"] in ["已提醒", "已过期"]])

        status_text = f"总计: {total} | 待提醒: {pending} | 已完成: {completed}"
        self.status_label.config(text=status_text)

        # 定期更新
        self.root.after(60000, self.update_status)  # 每分钟更新一次

    def load_memos(self):
        """加载备忘录数据"""
        try:
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    memos = json.load(f)
                    # 确保每个备忘录都有sound字段
                    for memo in memos:
                        if "sound" not in memo:
                            memo["sound"] = "system"
                    return memos
        except Exception as e:
            print(f"加载数据失败: {e}")
        return []

    def save_memos(self):
        """保存备忘录数据"""
        try:
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(self.memos, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存数据失败: {e}")

    def start_reminder_thread(self):
        """启动提醒线程"""
        self.reminder_thread = threading.Thread(target=self.reminder_loop, daemon=True)
        self.reminder_thread.start()

    def reminder_loop(self):
        """提醒循环 - 精确到分钟检查"""
        while True:
            try:
                current_time = datetime.now()
                for memo in self.memos:
                    if memo["status"] == "待提醒":
                        remind_time = datetime.strptime(memo["remind_time"], "%Y-%m-%d %H:%M")
                        # 精确到分钟的比较
                        if current_time.replace(second=0, microsecond=0) >= remind_time:
                            self.show_reminder(memo)
                            memo["status"] = "已提醒"
                            self.save_memos()
                            # 更新UI
                            self.root.after(0, self.refresh_memo_list)
                            self.root.after(0, self.update_status)

                time.sleep(30)  # 每30秒检查一次
            except Exception as e:
                print(f"提醒线程错误: {e}")
                time.sleep(60)

    def show_reminder(self, memo):
        """显示提醒"""
        # 播放自定义声音
        sound_name = memo.get("sound", "system")
        self.play_sound(sound_name)

        # 显示提醒窗口
        self.root.after(0, lambda: self.show_reminder_window(memo))

    def show_reminder_window(self, memo):
        """显示提醒窗口 - 科技感风格"""
        reminder_window = tk.Toplevel(self.root)
        reminder_window.title("⏰ 智能提醒")
        reminder_window.geometry("450x300")
        reminder_window.resizable(False, False)
        reminder_window.configure(bg=self.bg_color)
        reminder_window.attributes("-topmost", True)

        # 居中显示
        reminder_window.update_idletasks()
        x = (reminder_window.winfo_screenwidth() // 2) - (450 // 2)
        y = (reminder_window.winfo_screenheight() // 2) - (300 // 2)
        reminder_window.geometry(f"450x300+{x}+{y}")

        main_frame = ttk.Frame(reminder_window, style='Tech.TFrame', padding="25")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 提醒标题
        title_frame = ttk.Frame(main_frame, style='Tech.TFrame')
        title_frame.pack(fill=tk.X, pady=(0, 20))

        # 大号提醒图标
        icon_label = ttk.Label(title_frame, text="⏰",
                              font=('Segoe UI', 32),
                              style='Tech.TLabel')
        icon_label.pack(side=tk.LEFT)

        title_info = ttk.Frame(title_frame, style='Tech.TFrame')
        title_info.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(15, 0))

        ttk.Label(title_info, text="智能提醒",
                 font=('Segoe UI', 18, 'bold'),
                 style='TechTitle.TLabel').pack(anchor=tk.W)

        ttk.Label(title_info, text=f"提醒时间: {memo['remind_time']}",
                 font=('Segoe UI', 10),
                 style='Tech.TLabel').pack(anchor=tk.W, pady=(5, 0))

        # 任务内容
        content_frame = ttk.LabelFrame(main_frame, text="📝 任务内容",
                                      style='Tech.TLabelframe', padding="15")
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        content_text = tk.Text(content_frame, height=5, width=45,
                              font=('Segoe UI', 12),
                              bg=self.secondary_bg,
                              fg=self.text_color,
                              wrap=tk.WORD, relief='solid', bd=1)
        content_text.pack(fill=tk.BOTH, expand=True)
        content_text.insert("1.0", memo["content"])
        content_text.config(state=tk.DISABLED)

        # 按钮区域
        button_frame = ttk.Frame(main_frame, style='Tech.TFrame')
        button_frame.pack(fill=tk.X)

        # 声音信息
        sound_info = ttk.Label(button_frame,
                              text=f"🔊 {memo.get('sound', 'system')}",
                              style='TechSecondary.TLabel')
        sound_info.pack(side=tk.LEFT)

        # 操作按钮
        ttk.Button(button_frame, text="⏰ 稍后提醒",
                  command=lambda: self.snooze_reminder_window(memo, reminder_window),
                  style='Tech.TButton').pack(side=tk.RIGHT, padx=(10, 0))

        ttk.Button(button_frame, text="✅ 知道了",
                  command=reminder_window.destroy,
                  style='TechSuccess.TButton').pack(side=tk.RIGHT)

    def snooze_reminder_window(self, memo, window):
        """从提醒窗口延后提醒"""
        window.destroy()
        self.snooze_memo(memo)

    def show_settings(self):
        """显示设置窗口"""
        settings_window = tk.Toplevel(self.root)
        settings_window.title("⚙️ 应用设置")
        settings_window.geometry("400x300")
        settings_window.resizable(False, False)
        settings_window.configure(bg=self.bg_color)
        settings_window.transient(self.root)
        settings_window.grab_set()

        # 居中显示
        settings_window.update_idletasks()
        x = (settings_window.winfo_screenwidth() // 2) - (400 // 2)
        y = (settings_window.winfo_screenheight() // 2) - (300 // 2)
        settings_window.geometry(f"400x300+{x}+{y}")

        main_frame = ttk.Frame(settings_window, style='Tech.TFrame', padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = ttk.Label(main_frame, text="⚙️ 应用设置",
                               style='TechTitle.TLabel')
        title_label.pack(pady=(0, 20))

        # 设置选项
        # 默认声音设置
        sound_frame = ttk.LabelFrame(main_frame, text="默认声音设置",
                                    style='Tech.TLabelframe', padding="15")
        sound_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(sound_frame, text="默认提醒声音:", style='Tech.TLabel').pack(anchor=tk.W)

        default_sound_var = tk.StringVar(value=self.config.get("default_sound", "system"))
        default_sound_combo = ttk.Combobox(sound_frame, textvariable=default_sound_var,
                                          state="readonly", width=30)
        default_sound_combo['values'] = self.sound_combo['values']
        default_sound_combo.pack(anchor=tk.W, pady=(5, 0))

        # 检查间隔设置
        interval_frame = ttk.LabelFrame(main_frame, text="检查间隔设置",
                                       style='Tech.TLabelframe', padding="15")
        interval_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(interval_frame, text="提醒检查间隔(秒):", style='Tech.TLabel').pack(anchor=tk.W)

        interval_var = tk.StringVar(value=str(self.config.get("reminder_interval", 30)))
        interval_spin = ttk.Spinbox(interval_frame, from_=10, to=300,
                                   width=10, textvariable=interval_var,
                                   style='Tech.TSpinbox')
        interval_spin.pack(anchor=tk.W, pady=(5, 0))

        # 按钮
        button_frame = ttk.Frame(main_frame, style='Tech.TFrame')
        button_frame.pack(fill=tk.X, pady=(20, 0))

        def save_settings():
            self.config["default_sound"] = default_sound_var.get()
            self.config["reminder_interval"] = int(interval_var.get())
            self.save_config()

            # 更新默认声音选择
            self.sound_var.set(default_sound_var.get())

            settings_window.destroy()
            messagebox.showinfo("成功", "设置保存成功！")

        ttk.Button(button_frame, text="💾 保存设置",
                  command=save_settings,
                  style='TechSuccess.TButton').pack(side=tk.RIGHT, padx=(10, 0))

        ttk.Button(button_frame, text="❌ 取消",
                  command=settings_window.destroy,
                  style='Tech.TButton').pack(side=tk.RIGHT)

    def minimize_to_tray(self):
        """最小化到系统托盘"""
        if TRAY_AVAILABLE:
            self.root.withdraw()
            if not self.tray_icon:
                self.tray_icon = self.create_tray_icon()
                threading.Thread(target=self.tray_icon.run, daemon=True).start()
        else:
            self.hide_window()

    def hide_window(self):
        """隐藏窗口"""
        self.root.withdraw()
        messagebox.showinfo("提示", "程序已隐藏，可通过任务栏恢复")

    def create_tray_icon(self):
        """创建系统托盘图标"""
        if not TRAY_AVAILABLE:
            return None

        # 创建图标图像
        image = Image.new('RGB', (64, 64), color=(26, 26, 26))  # 深色背景
        draw = ImageDraw.Draw(image)

        # 绘制科技感圆形
        draw.ellipse([8, 8, 56, 56], fill=(0, 212, 255), outline=(0, 184, 230), width=2)

        # 绘制备忘录图标
        draw.text((20, 18), "📝", fill='white')

        # 创建菜单
        menu = pystray.Menu(
            pystray.MenuItem("🔮 显示窗口", self.show_window),
            pystray.MenuItem("➕ 添加任务", self.show_add_memo_dialog),
            pystray.Menu.SEPARATOR,
            pystray.MenuItem("⚙️ 设置", self.show_settings),
            pystray.MenuItem("❌ 退出", self.quit_app)
        )

        return pystray.Icon("智能备忘录", image, "智能备忘录", menu)

    def show_window(self, icon=None, item=None):
        """显示窗口"""
        self.root.deiconify()
        self.root.lift()
        self.root.focus_force()

    def show_add_memo_dialog(self, icon=None, item=None):
        """显示添加备忘对话框"""
        self.show_window()
        self.memo_text.focus_set()

    def quit_app(self, icon=None, item=None):
        """退出应用"""
        if self.tray_icon:
            self.tray_icon.stop()
        self.root.quit()

    def run(self):
        """运行应用程序"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

    def on_closing(self):
        """关闭程序时的处理"""
        if TRAY_AVAILABLE:
            # 如果支持托盘，询问是否最小化到托盘
            result = messagebox.askyesnocancel("退出确认",
                                             "选择退出方式：\n\n"
                                             "是 - 最小化到系统托盘\n"
                                             "否 - 完全退出程序\n"
                                             "取消 - 继续运行")
            if result is True:
                self.minimize_to_tray()
            elif result is False:
                self.quit_app()
        else:
            if messagebox.askokcancel("退出", "确定要退出智能备忘录吗？"):
                self.root.destroy()


def main():
    """主函数"""
    # 检查pygame依赖
    try:
        import pygame
    except ImportError:
        print("警告: pygame未安装，将使用系统默认声音")
        print("如需自定义声音功能，请运行: pip install pygame")

    app = MemoApp()
    app.run()


if __name__ == "__main__":
    main()
