@echo off
chcp 65001 >nul
title 备忘录小工具 - 手动打包

echo.
echo ========================================
echo     📦 备忘录小工具 - 手动打包
echo ========================================
echo.

echo 🔍 检查PyInstaller...
python -c "import PyInstaller; print('PyInstaller版本:', PyInstaller.__version__)" 2>nul
if errorlevel 1 (
    echo ❌ PyInstaller未安装
    echo 正在安装PyInstaller...
    pip install pyinstaller
    if errorlevel 1 (
        echo ❌ PyInstaller安装失败
        pause
        exit /b 1
    )
)

echo ✅ PyInstaller已准备就绪

echo.
echo 🧹 清理旧文件...
if exist build rmdir /s /q build
if exist dist rmdir /s /q dist
if exist __pycache__ rmdir /s /q __pycache__

echo.
echo 🔨 开始打包基础版本...
echo 命令: pyinstaller --onefile --windowed --name "备忘录" --icon memo.ico bwl.py
pyinstaller --onefile --windowed --name "备忘录" --icon memo.ico bwl.py

if errorlevel 1 (
    echo ❌ 基础版本打包失败
) else (
    echo ✅ 基础版本打包成功
)

echo.
echo 🔨 尝试打包增强版本...
python -c "import pystray, PIL" 2>nul
if errorlevel 1 (
    echo ⚠️ 缺少增强版依赖，跳过增强版打包
    echo 如需打包增强版，请运行: pip install pystray pillow
) else (
    echo 命令: pyinstaller --onefile --windowed --name "备忘录增强版" --icon memo.ico bwl_enhanced.py
    pyinstaller --onefile --windowed --name "备忘录增强版" --icon memo.ico bwl_enhanced.py
    
    if errorlevel 1 (
        echo ❌ 增强版本打包失败
    ) else (
        echo ✅ 增强版本打包成功
    )
)

echo.
echo 🔨 打包开机自启动设置工具...
pyinstaller --onefile --console --name "开机自启动设置" setup_autostart.py

echo.
echo 📦 创建便携版目录...
if not exist 便携版 mkdir 便携版

if exist dist\备忘录.exe (
    copy dist\备忘录.exe 便携版\
    echo   复制: 备忘录.exe
)

if exist dist\备忘录增强版.exe (
    copy dist\备忘录增强版.exe 便携版\
    echo   复制: 备忘录增强版.exe
)

if exist dist\开机自启动设置.exe (
    copy dist\开机自启动设置.exe 便携版\
    echo   复制: 开机自启动设置.exe
)

if exist README.md (
    copy README.md 便携版\
    echo   复制: README.md
)

if exist 安装指南.md (
    copy 安装指南.md 便携版\
    echo   复制: 安装指南.md
)

echo.
echo ========================================
echo 📊 打包完成总结
echo ========================================

echo.
echo 📁 生成的文件:
if exist dist (
    dir /b dist\*.exe
)

echo.
echo 📦 便携版目录:
if exist 便携版 (
    dir /b 便携版\*.*
)

echo.
echo 🎉 打包完成！
echo 📂 可执行文件位置: dist\
echo 📦 便携版位置: 便携版\
echo.
echo ⚠️ 注意事项:
echo 1. 首次运行可能被杀毒软件拦截，请添加信任
echo 2. 程序数据保存在memo_data.json文件中
echo 3. 开机自启动需要管理员权限

pause
