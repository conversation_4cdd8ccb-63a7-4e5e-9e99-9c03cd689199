@echo off
chcp 65001 >nul
title 智能备忘录 - 高级版

echo.
echo ========================================
echo     🔮 智能备忘录 - 高级版启动器
echo ========================================
echo.

echo 🔍 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    echo 请先安装Python并添加到系统PATH
    pause
    exit /b 1
)

echo ✅ Python环境正常

echo.
echo 🔍 检查pygame依赖...
python -c "import pygame" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ pygame未安装，正在安装...
    pip install pygame
    if errorlevel 1 (
        echo ❌ pygame安装失败
        echo 将使用系统默认声音功能
    )
) else (
    echo ✅ pygame已安装
)

echo.
echo 🚀 启动智能备忘录高级版...
python bwl_advanced.py

if errorlevel 1 (
    echo.
    echo ❌ 程序启动失败！
    echo 请检查：
    echo 1. 是否已安装Python
    echo 2. 是否在正确的目录下运行
    echo 3. bwl_advanced.py文件是否存在
    echo.
    pause
) else (
    echo.
    echo ✅ 程序已正常退出
)
