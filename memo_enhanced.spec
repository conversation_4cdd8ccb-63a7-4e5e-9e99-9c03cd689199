# -*- mode: python ; coding: utf-8 -*-
"""
备忘录小工具增强版 PyInstaller 配置文件
包含系统托盘功能
"""

import sys
from pathlib import Path

# 获取当前目录
current_dir = Path.cwd()

# 分析增强版程序
a = Analysis(
    ['bwl_enhanced.py'],
    pathex=[str(current_dir)],
    binaries=[],
    datas=[
        # 包含README等文档文件
        ('README.md', '.'),
        ('安装指南.md', '.'),
    ],
    hiddenimports=[
        # 确保包含所有需要的模块
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'datetime',
        'json',
        'threading',
        'time',
        'os',
        'sys',
        'winsound',
        'pathlib',
        'winreg',
        # 增强功能模块
        'pystray',
        'PIL',
        'PIL.Image',
        'PIL.ImageDraw',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # 排除不需要的模块以减小体积
        'matplotlib',
        'numpy',
        'scipy',
        'pandas',
        'test',
        'unittest',
        'distutils',
        'setuptools',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# 处理PYZ
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# 创建EXE
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='备忘录增强版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,  # 使用UPX压缩（如果可用）
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='memo.ico' if Path('memo.ico').exists() else None,  # 如果有图标文件
    version_file='version_info.txt' if Path('version_info.txt').exists() else None,
)
