@echo off
chcp 65001 >nul
title 备忘录小工具 - 快速打包

echo.
echo ========================================
echo     📦 备忘录小工具 - 快速打包
echo ========================================
echo.

echo 🔍 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    echo 请先安装Python并添加到系统PATH
    pause
    exit /b 1
)

echo ✅ Python环境正常

echo.
echo 📦 开始自动化打包...
python build.py

if errorlevel 1 (
    echo.
    echo ❌ 打包失败！
    echo 请检查错误信息并重试
) else (
    echo.
    echo ✅ 打包完成！
    echo 📂 可执行文件位置: dist\
    echo 📦 便携版位置: 便携版\
)

echo.
pause
