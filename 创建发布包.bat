@echo off
chcp 65001 >nul
title 智能备忘录高级版 - 创建发布包

echo.
echo ========================================
echo     🔮 智能备忘录高级版 - 创建发布包
echo ========================================
echo.

echo 📦 准备创建发布包...

if not exist 高级版便携包 (
    echo ❌ 高级版便携包目录不存在
    echo 请先运行打包程序
    pause
    exit /b 1
)

echo ✅ 高级版便携包目录存在

echo.
echo 📁 检查便携包内容...
dir /b 高级版便携包

echo.
echo 📊 文件大小统计...
for %%f in (高级版便携包\*.exe) do (
    echo   %%~nf.exe: %%~zf 字节
)

echo.
echo 🗜️ 创建压缩包...

REM 检查是否有7zip
where 7z >nul 2>&1
if errorlevel 1 (
    echo ⚠️ 7-Zip未安装，使用PowerShell压缩...
    
    powershell -command "Compress-Archive -Path '高级版便携包\*' -DestinationPath '智能备忘录高级版_v2.0.0_便携版.zip' -Force"
    
    if errorlevel 1 (
        echo ❌ 压缩失败
        pause
        exit /b 1
    ) else (
        echo ✅ 压缩包创建成功: 智能备忘录高级版_v2.0.0_便携版.zip
    )
) else (
    echo ✅ 使用7-Zip压缩...
    7z a -tzip "智能备忘录高级版_v2.0.0_便携版.zip" "高级版便携包\*"
    
    if errorlevel 1 (
        echo ❌ 压缩失败
        pause
        exit /b 1
    ) else (
        echo ✅ 压缩包创建成功: 智能备忘录高级版_v2.0.0_便携版.zip
    )
)

echo.
echo 📝 创建发布说明...
(
echo # 🔮 智能备忘录高级版 v2.0.0 - 发布说明
echo.
echo ## 📦 发布内容
echo.
echo ### 主要文件
echo - **智能备忘录高级版.exe** ^(22MB^) - 主程序
echo - **开机自启动设置.exe** ^(7.5MB^) - 自启动工具
echo - **使用说明.txt** - 快速使用指南
echo - **高级版功能说明.md** - 详细功能介绍
echo.
echo ## ✨ 新增功能
echo.
echo 1. **🔊 自定义声音上传** - 支持WAV/MP3/OGG格式
echo 2. **🗑️ 任务自由删除** - 右键菜单+批量删除
echo 3. **⏰ 时间精确到分钟** - 五级时间设置+快捷按钮
echo 4. **🎨 科技感金属简约风格UI** - 深色主题+现代化界面
echo.
echo ## 🚀 快速开始
echo.
echo 1. 解压文件到任意目录
echo 2. 双击"智能备忘录高级版.exe"启动
echo 3. 阅读"使用说明.txt"了解功能
echo.
echo ## ⚠️ 系统要求
echo.
echo - Windows 10/11 ^(64位^)
echo - 内存: 512MB以上
echo - 存储: 50MB可用空间
echo - 音频设备支持
echo.
echo ## 📞 注意事项
echo.
echo - 首次运行可能被杀毒软件拦截，请添加信任
echo - 开机自启动需要管理员权限
echo - 建议定期备份数据文件
echo.
echo ---
echo 发布日期: %date%
echo 版本: v2.0.0 高级版
echo 🎉 感谢使用智能备忘录高级版！
) > 发布说明.md

echo   创建: 发布说明.md

echo.
echo 📊 最终统计...
if exist "智能备忘录高级版_v2.0.0_便携版.zip" (
    for %%f in ("智能备忘录高级版_v2.0.0_便携版.zip") do (
        echo   压缩包大小: %%~zf 字节 ^(约 %%~zf/1048576 MB^)
    )
)

echo.
echo ========================================
echo 🎉 发布包创建完成！
echo ========================================
echo.
echo 📦 发布文件:
echo   - 智能备忘录高级版_v2.0.0_便携版.zip
echo   - 发布说明.md
echo.
echo 📂 原始文件:
echo   - 高级版便携包\ ^(目录^)
echo   - dist\ ^(构建输出^)
echo.
echo ✨ 新功能特色:
echo   🔊 自定义声音上传
echo   🗑️ 任务自由删除  
echo   ⏰ 时间精确到分钟
echo   🎨 科技感金属简约风格UI
echo.
echo 🚀 现在可以分发智能备忘录高级版了！

pause
