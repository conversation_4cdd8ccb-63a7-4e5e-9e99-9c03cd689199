#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设置备忘录程序开机自启动
"""

import os
import sys
import winreg
from pathlib import Path

def add_to_startup():
    """添加程序到开机自启动"""
    try:
        # 获取当前脚本的完整路径
        current_dir = Path(__file__).parent.absolute()
        script_path = current_dir / "bwl.py"
        python_exe = sys.executable
        
        # 创建启动命令
        startup_command = f'"{python_exe}" "{script_path}"'
        
        # 打开注册表项
        key = winreg.OpenKey(
            winreg.HKEY_CURRENT_USER,
            r"Software\Microsoft\Windows\CurrentVersion\Run",
            0,
            winreg.KEY_SET_VALUE
        )
        
        # 设置注册表值
        winreg.SetValueEx(key, "备忘录", 0, winreg.REG_SZ, startup_command)
        winreg.CloseKey(key)
        
        print("✅ 开机自启动设置成功！")
        print(f"启动命令: {startup_command}")
        return True
        
    except Exception as e:
        print(f"❌ 设置开机自启动失败: {e}")
        return False

def remove_from_startup():
    """从开机自启动中移除程序"""
    try:
        # 打开注册表项
        key = winreg.OpenKey(
            winreg.HKEY_CURRENT_USER,
            r"Software\Microsoft\Windows\CurrentVersion\Run",
            0,
            winreg.KEY_SET_VALUE
        )
        
        # 删除注册表值
        winreg.DeleteValue(key, "备忘录")
        winreg.CloseKey(key)
        
        print("✅ 已从开机自启动中移除！")
        return True
        
    except FileNotFoundError:
        print("ℹ️ 程序未设置开机自启动")
        return True
    except Exception as e:
        print(f"❌ 移除开机自启动失败: {e}")
        return False

def check_startup_status():
    """检查开机自启动状态"""
    try:
        key = winreg.OpenKey(
            winreg.HKEY_CURRENT_USER,
            r"Software\Microsoft\Windows\CurrentVersion\Run",
            0,
            winreg.KEY_READ
        )
        
        try:
            value, _ = winreg.QueryValueEx(key, "备忘录")
            winreg.CloseKey(key)
            print(f"✅ 已设置开机自启动: {value}")
            return True
        except FileNotFoundError:
            winreg.CloseKey(key)
            print("❌ 未设置开机自启动")
            return False
            
    except Exception as e:
        print(f"❌ 检查开机自启动状态失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("备忘录程序 - 开机自启动设置工具")
    print("=" * 50)
    
    while True:
        print("\n请选择操作:")
        print("1. 设置开机自启动")
        print("2. 移除开机自启动")
        print("3. 检查自启动状态")
        print("4. 退出")
        
        choice = input("\n请输入选项 (1-4): ").strip()
        
        if choice == "1":
            add_to_startup()
        elif choice == "2":
            remove_from_startup()
        elif choice == "3":
            check_startup_status()
        elif choice == "4":
            print("再见！")
            break
        else:
            print("❌ 无效选项，请重新输入")

if __name__ == "__main__":
    main()
