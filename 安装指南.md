# 📝 备忘录小工具 - 安装指南

## 🎯 快速开始

### 方法一：直接运行（推荐）
1. **双击运行** `启动备忘录.bat` 文件
2. 如果出现错误，请按照下面的详细安装步骤操作

### 方法二：命令行运行
```bash
python bwl.py
```

## 🛠️ 详细安装步骤

### 1. 检查Python环境
确保您的系统已安装Python 3.7或更高版本：
```bash
python --version
```

如果未安装Python，请从 [python.org](https://www.python.org/downloads/) 下载安装。

### 2. 选择版本运行

#### 基础版本（推荐）
- 文件：`bwl.py`
- 特点：使用Python标准库，无需额外依赖
- 运行：`python bwl.py`

#### 增强版本（可选）
- 文件：`bwl_enhanced.py`
- 特点：支持真正的系统托盘功能
- 需要安装额外依赖：
```bash
pip install pystray pillow
```
- 运行：`python bwl_enhanced.py`

### 3. 设置开机自启动
```bash
python setup_autostart.py
```
按照提示选择相应选项。

## 📋 功能说明

### ✨ 主要功能
- ✅ 添加多个备忘任务
- ⏰ 设置具体的提醒日期和时间
- 🔊 到期声音提醒
- 📱 弹窗提醒显示
- 💾 自动保存数据
- 🚀 开机自启动

### 🎨 界面特点
- 极简设计风格
- 清爽的配色方案
- 直观的操作界面
- 友好的用户体验

### 🔧 操作指南
1. **添加备忘**：输入内容，设置时间，点击添加
2. **查看详情**：双击列表中的项目
3. **删除备忘**：选中后点击删除按钮
4. **状态说明**：
   - ⏳ 待提醒：等待提醒时间到达
   - ✅ 已提醒：已经提醒过
   - ❌ 已过期：超过提醒时间但未提醒

## ⚠️ 注意事项

### 系统要求
- Windows 10/11
- Python 3.7+
- 建议内存：512MB以上

### 使用建议
1. **时间设置**：提醒时间必须晚于当前时间
2. **数据安全**：重要备忘建议定期备份
3. **权限要求**：开机自启需要管理员权限
4. **声音设置**：确保系统音量开启

### 常见问题

#### Q: 程序无法启动？
A: 检查Python版本，确保为3.7+

#### Q: 没有声音提醒？
A: 检查系统音量和声音方案设置

#### Q: 开机自启动失败？
A: 以管理员权限运行设置脚本

#### Q: 数据丢失怎么办？
A: 查看是否存在 `memo_data.json` 备份文件

## 📁 文件结构

```
beiwanglu/
├── bwl.py                 # 主程序（基础版）
├── bwl_enhanced.py        # 增强版程序
├── setup_autostart.py    # 开机自启设置
├── 启动备忘录.bat         # 快速启动脚本
├── requirements.txt       # 依赖说明
├── README.md             # 详细说明
├── 安装指南.md           # 本文件
└── memo_data.json        # 数据文件（自动生成）
```

## 🔄 更新升级

### 保留数据升级
1. 备份 `memo_data.json` 文件
2. 下载新版本程序文件
3. 将备份的数据文件放回目录
4. 重新运行程序

### 完全重装
1. 删除所有程序文件
2. 重新下载完整包
3. 按照安装步骤重新设置

## 🆘 技术支持

如果遇到问题，请检查：
1. Python版本是否正确
2. 文件是否完整
3. 权限是否足够
4. 系统兼容性

## 📝 版本信息

- 当前版本：v1.0.0
- 发布日期：2024年
- 兼容系统：Windows 10/11
- Python要求：3.7+

---

🎉 **祝您使用愉快！**
