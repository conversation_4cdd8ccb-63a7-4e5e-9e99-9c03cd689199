# -*- mode: python ; coding: utf-8 -*-
"""
备忘录小工具 PyInstaller 配置文件
"""

import sys
from pathlib import Path

# 获取当前目录
current_dir = Path.cwd()

# 分析主程序
a = Analysis(
    ['bwl.py'],
    pathex=[str(current_dir)],
    binaries=[],
    datas=[
        # 包含README等文档文件
        ('README.md', '.'),
        ('安装指南.md', '.'),
    ],
    hiddenimports=[
        # 确保包含所有需要的模块
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'datetime',
        'json',
        'threading',
        'time',
        'os',
        'sys',
        'winsound',
        'pathlib',
        'winreg',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # 排除不需要的模块以减小体积
        'matplotlib',
        'numpy',
        'scipy',
        'pandas',
        'PIL',
        'pystray',
        'test',
        'unittest',
        'distutils',
        'setuptools',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# 处理PYZ
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# 创建EXE
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='备忘录',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,  # 使用UPX压缩（如果可用）
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='memo.ico' if Path('memo.ico').exists() else None,  # 如果有图标文件
    version_file='version_info.txt' if Path('version_info.txt').exists() else None,
)

# 如果需要创建目录分发版本，取消注释以下代码
# coll = COLLECT(
#     exe,
#     a.binaries,
#     a.zipfiles,
#     a.datas,
#     strip=False,
#     upx=True,
#     upx_exclude=[],
#     name='备忘录'
# )
